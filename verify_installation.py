#!/usr/bin/env python3
"""
Simple verification script for the high-performance rotation system
"""

print("🎭 THEATER PROJECTION SYSTEM")
print("High-Performance Rotation Verification")
print("=" * 50)

# Test 1: Basic imports
print("\n1️⃣ Testing basic imports...")
try:
    import numpy as np
    import cv2
    import tkinter as tk
    from PIL import Image, ImageTk
    print("   ✅ Core libraries imported successfully")
except ImportError as e:
    print(f"   ❌ Import error: {e}")
    exit(1)

# Test 2: Performance libraries
print("\n2️⃣ Testing performance libraries...")
try:
    import numba
    print(f"   ✅ Numba {numba.__version__} available (JIT compilation)")
    numba_available = True
except ImportError:
    print("   ⚠️ Numba not available - using standard rotation")
    numba_available = False

try:
    import psutil
    memory_gb = psutil.virtual_memory().total / (1024**3)
    print(f"   ✅ psutil available - {memory_gb:.1f}GB RAM detected")
except ImportError:
    print("   ⚠️ psutil not available - limited system monitoring")

# Test 3: External tools
print("\n3️⃣ Testing external tools...")
try:
    import subprocess
    result = subprocess.run(['magick', '-version'], capture_output=True, timeout=5)
    if result.returncode == 0:
        version_line = result.stdout.decode().split('\n')[0]
        print(f"   ✅ ImageMagick available: {version_line}")
    else:
        print("   ⚠️ ImageMagick not found - external rotation unavailable")
except:
    print("   ⚠️ ImageMagick not found - external rotation unavailable")

# Test 4: Basic rotation performance
print("\n4️⃣ Testing rotation performance...")
try:
    test_image = np.random.randint(0, 255, (540, 960, 3), dtype=np.uint8)  # Smaller test
    
    import time
    start_time = time.perf_counter()
    
    for i in range(5):  # Fewer iterations for quick test
        center = (480, 270)
        rotation_matrix = cv2.getRotationMatrix2D(center, i * 72, 1.0)
        rotated = cv2.warpAffine(test_image, rotation_matrix, (960, 540))
    
    total_time = time.perf_counter() - start_time
    fps = 5 / total_time
    
    print(f"   ⏱️ Basic rotation: {fps:.1f} FPS")
    
    if fps > 100:
        print("   🟢 EXCELLENT performance - smooth rotation guaranteed")
    elif fps > 50:
        print("   🟡 GOOD performance - smooth rotation expected")
    elif fps > 20:
        print("   🟠 FAIR performance - may need optimization")
    else:
        print("   🔴 LOW performance - consider system optimization")
        
except Exception as e:
    print(f"   ❌ Rotation test failed: {e}")

# Test 5: Main application import
print("\n5️⃣ Testing main application...")
try:
    import main
    print("   ✅ Main application imports successfully")
    
    # Check if UltimateRotationEngine can be created
    if hasattr(main, 'UltimateRotationEngine'):
        print("   ✅ UltimateRotationEngine class available")
    else:
        print("   ❌ UltimateRotationEngine class not found")
        
except Exception as e:
    print(f"   ❌ Main application import failed: {e}")

# Summary
print("\n" + "=" * 50)
print("📋 VERIFICATION SUMMARY")

if numba_available:
    print("🚀 HIGH-PERFORMANCE MODE: Numba JIT compilation available")
else:
    print("🔧 STANDARD MODE: Using OpenCV rotation")

print("\n💡 RECOMMENDATIONS:")
print("   1. Run 'python main.py' to start the application")
print("   2. Add images/videos to test rotation")
print("   3. Monitor performance in the status bar")
print("   4. Adjust quality settings if needed")

print("\n🎉 System verification complete!")
print("Your Intel i5-1235U system is ready for high-performance rotation!")
