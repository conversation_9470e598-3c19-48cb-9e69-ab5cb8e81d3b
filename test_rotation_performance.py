#!/usr/bin/env python3
"""
Test script to verify high-performance rotation system
"""

import numpy as np
import cv2
import time
import sys
import os

# Add the current directory to path to import from main.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_rotation_backends():
    """Test all available rotation backends"""
    print("🧪 ROTATION PERFORMANCE TEST")
    print("=" * 50)
    
    # Create test image (1080p)
    test_image = np.random.randint(0, 255, (1080, 1920, 3), dtype=np.uint8)
    print(f"📊 Test image: {test_image.shape} ({test_image.nbytes / 1024 / 1024:.1f} MB)")
    
    # Test standard OpenCV rotation
    print("\n🔧 Testing Standard OpenCV Rotation...")
    start_time = time.perf_counter()
    
    for i in range(20):
        angle = i * 18  # 20 rotations covering 360 degrees
        center = (960, 540)
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
        rotated = cv2.warpAffine(test_image, rotation_matrix, (1920, 1080), 
                                flags=cv2.INTER_LINEAR)
    
    opencv_time = time.perf_counter() - start_time
    opencv_fps = 20 / opencv_time
    
    print(f"   ⏱️ Time: {opencv_time:.3f}s for 20 rotations")
    print(f"   🎯 Performance: {opencv_fps:.1f} FPS")
    
    # Test Numba if available
    try:
        from main import NUMBA_AVAILABLE, numba_rotate_bilinear
        if NUMBA_AVAILABLE:
            print("\n🚀 Testing Numba JIT Rotation...")
            
            # First call to compile the function
            angle_rad = np.radians(45)
            center_x, center_y = 960, 540
            _ = numba_rotate_bilinear(test_image, angle_rad, center_x, center_y)
            
            # Now test performance
            start_time = time.perf_counter()
            
            for i in range(20):
                angle_rad = np.radians(i * 18)
                rotated = numba_rotate_bilinear(test_image, angle_rad, center_x, center_y)
            
            numba_time = time.perf_counter() - start_time
            numba_fps = 20 / numba_time
            
            print(f"   ⏱️ Time: {numba_time:.3f}s for 20 rotations")
            print(f"   🎯 Performance: {numba_fps:.1f} FPS")
            print(f"   🚀 Speedup: {opencv_time/numba_time:.1f}x faster than OpenCV")
        else:
            print("\n⚠️ Numba not available - install with: pip install numba")
            
    except ImportError as e:
        print(f"\n❌ Could not test Numba: {e}")
    
    # Test buffer manager
    try:
        from main import RotationBufferManager
        print("\n💾 Testing Buffer Manager...")
        
        buffer_manager = RotationBufferManager(max_width=1920, max_height=1080)
        
        start_time = time.perf_counter()
        
        for i in range(20):
            angle = i * 18
            buffers = buffer_manager.get_buffers(1920, 1080)
            rotation_matrix = buffer_manager.get_rotation_matrix(1920, 1080, angle)
            
            cv2.warpAffine(test_image, rotation_matrix, (1920, 1080),
                          dst=buffers['output'], flags=cv2.INTER_LINEAR)
        
        buffer_time = time.perf_counter() - start_time
        buffer_fps = 20 / buffer_time
        
        print(f"   ⏱️ Time: {buffer_time:.3f}s for 20 rotations")
        print(f"   🎯 Performance: {buffer_fps:.1f} FPS")
        print(f"   💾 Speedup: {opencv_time/buffer_time:.1f}x faster than standard OpenCV")
        
    except ImportError as e:
        print(f"\n❌ Could not test Buffer Manager: {e}")
    
    # Performance summary
    print("\n" + "=" * 50)
    print("📋 PERFORMANCE SUMMARY")
    print(f"🔧 Standard OpenCV: {opencv_fps:.1f} FPS")
    
    try:
        if NUMBA_AVAILABLE:
            print(f"🚀 Numba JIT: {numba_fps:.1f} FPS ({numba_fps/opencv_fps:.1f}x)")
    except:
        pass
    
    try:
        print(f"💾 Buffer Manager: {buffer_fps:.1f} FPS ({buffer_fps/opencv_fps:.1f}x)")
    except:
        pass
    
    # Performance rating
    best_fps = opencv_fps
    try:
        if NUMBA_AVAILABLE:
            best_fps = max(best_fps, numba_fps)
        best_fps = max(best_fps, buffer_fps)
    except:
        pass
    
    if best_fps > 80:
        rating = "🟢 EXCELLENT"
    elif best_fps > 60:
        rating = "🟡 GOOD"
    elif best_fps > 30:
        rating = "🟠 FAIR"
    else:
        rating = "🔴 NEEDS OPTIMIZATION"
    
    print(f"\n🎯 Overall Rating: {rating} (Best: {best_fps:.1f} FPS)")
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS:")
    if best_fps > 60:
        print("   ✅ Your system can handle smooth 60+ FPS rotation")
        print("   ✅ Use HIGH or ULTRA quality settings")
    elif best_fps > 30:
        print("   👍 Your system can handle smooth 30+ FPS rotation")
        print("   💡 Use MEDIUM quality for best balance")
    else:
        print("   ⚠️ Consider using FAST or FASTEST quality settings")
        print("   💡 Close other applications to free up resources")

def test_memory_usage():
    """Test memory usage of different approaches"""
    print("\n💾 MEMORY USAGE TEST")
    print("-" * 30)
    
    try:
        import psutil
        process = psutil.Process()
        
        # Baseline memory
        baseline_memory = process.memory_info().rss / 1024 / 1024
        print(f"📊 Baseline memory: {baseline_memory:.1f} MB")
        
        # Test PhotoImage pool
        from main import PhotoImagePool
        from PIL import Image
        
        pool = PhotoImagePool(pool_size=3)
        test_pil_image = Image.fromarray(np.random.randint(0, 255, (720, 1280, 3), dtype=np.uint8))
        
        # Create multiple PhotoImages
        photos = []
        for i in range(10):
            photo = pool.get_photo_image(test_pil_image)
            photos.append(photo)
        
        pooled_memory = process.memory_info().rss / 1024 / 1024
        print(f"💾 With PhotoImage pool: {pooled_memory:.1f} MB (+{pooled_memory-baseline_memory:.1f} MB)")
        
        print("✅ Memory management test completed")
        
    except ImportError:
        print("⚠️ psutil not available - cannot test memory usage")

if __name__ == "__main__":
    test_rotation_backends()
    test_memory_usage()
    
    print("\n🎉 Performance testing complete!")
    print("💡 Run main.py to use the Theater Projection System")
