#!/usr/bin/env python3
"""
Create a test image for rotation testing
"""

import numpy as np
from PIL import Image, ImageDraw, ImageFont
import os

def create_test_image():
    """Create a test image with clear rotation indicators"""
    
    # Create a 1920x1080 image
    width, height = 1920, 1080
    
    # Create image with gradient background
    image = Image.new('RGB', (width, height), color='black')
    draw = ImageDraw.Draw(image)
    
    # Create gradient background
    for y in range(height):
        color_value = int(255 * (y / height) * 0.3)  # Dark gradient
        draw.line([(0, y), (width, y)], fill=(color_value, color_value//2, color_value//3))
    
    # Draw rotation indicators
    center_x, center_y = width // 2, height // 2
    
    # Draw crosshairs
    draw.line([(center_x - 200, center_y), (center_x + 200, center_y)], fill='white', width=5)
    draw.line([(center_x, center_y - 200), (center_x, center_y + 200)], fill='white', width=5)
    
    # Draw corner markers
    corner_size = 100
    corners = [
        (50, 50, "TOP LEFT"),
        (width - 150, 50, "TOP RIGHT"),
        (50, height - 100, "BOTTOM LEFT"),
        (width - 200, height - 100, "BOTTOM RIGHT")
    ]
    
    for x, y, label in corners:
        # Draw colored rectangles
        colors = {'TOP LEFT': 'red', 'TOP RIGHT': 'green', 
                 'BOTTOM LEFT': 'blue', 'BOTTOM RIGHT': 'yellow'}
        draw.rectangle([x, y, x + corner_size, y + 50], fill=colors[label])
        
        # Add text
        try:
            # Try to use a larger font
            font = ImageFont.truetype("arial.ttf", 20)
        except:
            font = ImageFont.load_default()
        
        draw.text((x + 5, y + 55), label, fill='white', font=font)
    
    # Draw center circle
    circle_radius = 50
    draw.ellipse([center_x - circle_radius, center_y - circle_radius,
                  center_x + circle_radius, center_y + circle_radius], 
                 outline='cyan', width=5)
    
    # Add title text
    try:
        title_font = ImageFont.truetype("arial.ttf", 48)
    except:
        title_font = ImageFont.load_default()
    
    title_text = "ROTATION TEST IMAGE"
    # Get text size for centering
    bbox = draw.textbbox((0, 0), title_text, font=title_font)
    text_width = bbox[2] - bbox[0]
    text_x = (width - text_width) // 2
    
    draw.text((text_x, 150), title_text, fill='white', font=title_font)
    
    # Add performance info
    try:
        info_font = ImageFont.truetype("arial.ttf", 24)
    except:
        info_font = ImageFont.load_default()
    
    info_lines = [
        "🚀 High-Performance Rotation System",
        "🎯 Optimized for Intel 12th Gen i5-1235U",
        "💾 8GB RAM Optimized",
        "🎭 Theater Projection Ready"
    ]
    
    for i, line in enumerate(info_lines):
        bbox = draw.textbbox((0, 0), line, font=info_font)
        text_width = bbox[2] - bbox[0]
        text_x = (width - text_width) // 2
        draw.text((text_x, 250 + i * 40), line, fill='cyan', font=info_font)
    
    # Add rotation direction indicators
    # Draw arrows showing rotation direction
    arrow_points = [
        # Right arrow
        [(center_x + 300, center_y), (center_x + 350, center_y - 20), 
         (center_x + 350, center_y - 10), (center_x + 380, center_y - 10),
         (center_x + 380, center_y + 10), (center_x + 350, center_y + 10),
         (center_x + 350, center_y + 20)],
        # Down arrow  
        [(center_x, center_y + 300), (center_x - 20, center_y + 350),
         (center_x - 10, center_y + 350), (center_x - 10, center_y + 380),
         (center_x + 10, center_y + 380), (center_x + 10, center_y + 350),
         (center_x + 20, center_y + 350)]
    ]
    
    for arrow in arrow_points:
        draw.polygon(arrow, fill='orange', outline='white')
    
    return image

def main():
    """Create and save the test image"""
    print("🎨 Creating rotation test image...")
    
    # Create the test image
    test_image = create_test_image()
    
    # Save the image
    output_path = "rotation_test_image.png"
    test_image.save(output_path, "PNG", quality=95)
    
    print(f"✅ Test image created: {output_path}")
    print(f"📊 Image size: {test_image.size}")
    print(f"💾 File size: {os.path.getsize(output_path) / 1024:.1f} KB")
    
    print("\n🎭 USAGE INSTRUCTIONS:")
    print("1. Run: python main.py")
    print("2. Click 'Add Media Files' and select rotation_test_image.png")
    print("3. Set rotate_left or rotate_right values (e.g., 50)")
    print("4. Open projection window (Tools → Open Projection Window)")
    print("5. Press Play to see smooth rotation!")
    
    print("\n🎯 WHAT TO LOOK FOR:")
    print("- Smooth rotation without choppiness")
    print("- Corner markers should rotate clearly")
    print("- Performance indicator in status bar")
    print("- No lag or stuttering")

if __name__ == "__main__":
    main()
