#!/usr/bin/env python3
"""
Installation script for high-performance rotation dependencies
Optimized for Intel 12th Gen i5-1235U with 8GB RAM
"""

import subprocess
import sys
import os
import platform

def run_command(cmd, description):
    """Run a command and handle errors"""
    print(f"\n🔧 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            return True
        else:
            print(f"❌ {description} failed:")
            print(result.stderr)
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} timed out")
        return False
    except Exception as e:
        print(f"❌ {description} error: {e}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python {version.major}.{version.minor} detected. Python 3.8+ required.")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_numba():
    """Install Numba JIT compiler for maximum performance"""
    print("\n🚀 Installing Numba JIT compiler for ultra-fast rotation...")
    
    # Try different installation methods
    methods = [
        ("pip install numba", "Installing Numba via pip"),
        ("conda install numba -y", "Installing Numba via conda (if available)"),
        ("pip install --user numba", "Installing Numba for current user only")
    ]
    
    for cmd, desc in methods:
        if run_command(cmd, desc):
            # Test Numba installation
            try:
                import numba
                print(f"✅ Numba {numba.__version__} installed successfully")
                return True
            except ImportError:
                continue
    
    print("⚠️ Numba installation failed. Rotation will use standard OpenCV (still fast)")
    return False

def install_psutil():
    """Install psutil for system monitoring"""
    print("\n💾 Installing psutil for system monitoring...")
    
    methods = [
        ("pip install psutil", "Installing psutil via pip"),
        ("pip install --user psutil", "Installing psutil for current user")
    ]
    
    for cmd, desc in methods:
        if run_command(cmd, desc):
            try:
                import psutil
                print(f"✅ psutil installed successfully")
                return True
            except ImportError:
                continue
    
    print("⚠️ psutil installation failed. System monitoring will be limited")
    return False

def check_imagemagick():
    """Check if ImageMagick is available"""
    print("\n🎨 Checking for ImageMagick...")
    
    try:
        result = subprocess.run(['magick', '-version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ ImageMagick found: {version_line}")
            return True
    except (subprocess.TimeoutExpired, FileNotFoundError):
        pass
    
    print("⚠️ ImageMagick not found. External rotation will be unavailable.")
    print("💡 To install ImageMagick:")
    if platform.system() == "Windows":
        print("   - Download from: https://imagemagick.org/script/download.php#windows")
        print("   - Or use: winget install ImageMagick.ImageMagick")
    elif platform.system() == "Darwin":  # macOS
        print("   - Use Homebrew: brew install imagemagick")
    else:  # Linux
        print("   - Ubuntu/Debian: sudo apt install imagemagick")
        print("   - CentOS/RHEL: sudo yum install ImageMagick")
    
    return False

def optimize_for_intel_cpu():
    """Apply Intel-specific optimizations"""
    print("\n⚡ Applying Intel 12th Gen optimizations...")
    
    # Set environment variables for Intel optimizations
    intel_optimizations = {
        'OMP_NUM_THREADS': '6',  # Use 6 threads (leave 4 for system)
        'MKL_NUM_THREADS': '6',
        'NUMBA_NUM_THREADS': '6',
        'OPENCV_CPU_DISABLE': 'AVX512F',  # Disable AVX512 for better compatibility
    }
    
    print("🔧 Setting performance environment variables:")
    for key, value in intel_optimizations.items():
        os.environ[key] = value
        print(f"   {key} = {value}")
    
    print("✅ Intel optimizations applied for current session")
    print("💡 Add these to your system environment variables for permanent effect")

def test_performance():
    """Test the performance improvements"""
    print("\n🧪 Testing performance improvements...")
    
    try:
        import numpy as np
        import cv2
        import time
        
        # Create test image
        test_image = np.random.randint(0, 255, (1080, 1920, 3), dtype=np.uint8)
        
        # Test OpenCV rotation
        start_time = time.perf_counter()
        for i in range(10):
            center = (960, 540)
            rotation_matrix = cv2.getRotationMatrix2D(center, i * 36, 1.0)
            rotated = cv2.warpAffine(test_image, rotation_matrix, (1920, 1080))
        opencv_time = time.perf_counter() - start_time
        
        print(f"📊 OpenCV rotation test: {opencv_time:.3f}s for 10 rotations")
        print(f"   Average: {opencv_time/10*1000:.1f}ms per rotation")
        
        # Test Numba if available
        try:
            import numba
            print("🚀 Numba JIT compiler is available for maximum performance")
        except ImportError:
            print("⚠️ Numba not available - using standard OpenCV")
        
        # Performance rating
        fps_estimate = 1.0 / (opencv_time / 10)
        if fps_estimate > 100:
            rating = "🟢 EXCELLENT"
        elif fps_estimate > 60:
            rating = "🟡 GOOD"
        elif fps_estimate > 30:
            rating = "🟠 FAIR"
        else:
            rating = "🔴 NEEDS OPTIMIZATION"
        
        print(f"🎯 Performance rating: {rating} (~{fps_estimate:.0f} FPS potential)")
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")

def main():
    """Main installation process"""
    print("🎭 THEATER PROJECTION SYSTEM")
    print("High-Performance Rotation Setup")
    print("=" * 50)
    
    # Check system compatibility
    if not check_python_version():
        sys.exit(1)
    
    print(f"🖥️ System: {platform.system()} {platform.machine()}")
    print(f"💻 Optimizing for Intel 12th Gen i5-1235U")
    
    # Install dependencies
    success_count = 0
    total_deps = 3
    
    if install_numba():
        success_count += 1
    
    if install_psutil():
        success_count += 1
    
    if check_imagemagick():
        success_count += 1
    
    # Apply optimizations
    optimize_for_intel_cpu()
    
    # Test performance
    test_performance()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 INSTALLATION SUMMARY")
    print(f"✅ {success_count}/{total_deps} optional dependencies installed")
    
    if success_count == total_deps:
        print("🎉 Perfect! All performance features available")
    elif success_count >= 1:
        print("👍 Good! Core performance features available")
    else:
        print("⚠️ Basic functionality only - consider installing dependencies")
    
    print("\n🚀 Your system is ready for high-performance rotation!")
    print("💡 Run main.py to start the Theater Projection System")

if __name__ == "__main__":
    main()
