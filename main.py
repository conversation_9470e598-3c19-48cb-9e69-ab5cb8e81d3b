import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import threading
import time
import os
from PIL import Image, ImageTk
import cv2
import numpy as np
from pathlib import Path
import weakref
from concurrent.futures import ThreadPoolExecutor
import queue
import math
from dataclasses import dataclass
from typing import Optional, Callable, Dict, Any, Tuple
from enum import Enum
import ctypes
import platform
import asyncio
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor as ThreadPool

# Try to import performance libraries
try:
    import numba
    NUMBA_AVAILABLE = True
    print("🚀 Numba JIT compiler available - enabling high-performance rotation")
except ImportError:
    NUMBA_AVAILABLE = False
    print("⚠️ Numba not available - using standard rotation (install with: pip install numba)")

try:
    import subprocess
    SUBPROCESS_AVAILABLE = True
except ImportError:
    SUBPROCESS_AVAILABLE = False

# Suppress verbose debug logging
import builtins as _builtins

# Store the original print function
_original_print = _builtins.print

# ============================================================================
# ULTIMATE ROTATION SYSTEM - MAXIMUM PERFORMANCE & QUALITY
# Optimized for Intel 12th Gen i5-1235U with 8GB RAM
# ============================================================================

class RotationQuality(Enum):
    """Rotation quality levels for adaptive performance"""
    ULTRA = "ultra"      # LANCZOS + temporal smoothing
    HIGH = "high"        # LANCZOS
    MEDIUM = "medium"    # BICUBIC
    FAST = "fast"        # BILINEAR
    FASTEST = "fastest"  # NEAREST

class RotationBackend(Enum):
    """Available rotation backends"""
    OPENCV = "opencv"        # Fastest, best for real-time
    NUMBA = "numba"         # JIT-compiled for Intel CPU
    THREADED = "threaded"   # Multi-threaded OpenCV
    BUFFERED = "buffered"   # Pre-allocated buffers
    EXTERNAL = "external"   # ImageMagick subprocess

# High-performance buffer manager for zero-allocation rotation
class RotationBufferManager:
    """Pre-allocated buffer system optimized for Intel integrated graphics"""

    def __init__(self, max_width=1920, max_height=1080):
        self.max_width = max_width
        self.max_height = max_height

        # Pre-allocate buffers for common sizes
        self.buffers = {}
        self.rotation_matrices = {}
        self.temp_surfaces = {}

        # Initialize common buffer sizes
        common_sizes = [(1920, 1080), (1280, 720), (1024, 768), (800, 600)]
        for width, height in common_sizes:
            if width <= max_width and height <= max_height:
                self._init_buffers_for_size(width, height)

    def _init_buffers_for_size(self, width, height):
        """Initialize buffers for a specific size"""
        key = (width, height)

        # Pre-allocate numpy arrays
        self.buffers[key] = {
            'input': np.empty((height, width, 3), dtype=np.uint8),
            'output': np.empty((height, width, 3), dtype=np.uint8),
            'temp': np.empty((height, width, 3), dtype=np.uint8),
            'canvas': np.zeros((int(math.sqrt(width**2 + height**2)) + 10,
                              int(math.sqrt(width**2 + height**2)) + 10, 3), dtype=np.uint8)
        }

        # Pre-compute rotation matrices for common angles
        center = (width // 2, height // 2)
        self.rotation_matrices[key] = {}

        # Pre-compute every 0.5 degrees for smooth rotation
        for angle_int in range(0, 3600, 5):  # 0.5 degree steps
            angle = angle_int / 10.0
            self.rotation_matrices[key][angle] = cv2.getRotationMatrix2D(center, angle, 1.0)

    def get_buffers(self, width, height):
        """Get pre-allocated buffers for given size"""
        key = (width, height)
        if key not in self.buffers:
            self._init_buffers_for_size(width, height)
        return self.buffers[key]

    def get_rotation_matrix(self, width, height, angle):
        """Get cached rotation matrix"""
        key = (width, height)
        if key not in self.rotation_matrices:
            self._init_buffers_for_size(width, height)

        # Round to nearest 0.5 degrees for cache hit
        rounded_angle = round(angle * 2) / 2

        if rounded_angle not in self.rotation_matrices[key]:
            center = (width // 2, height // 2)
            self.rotation_matrices[key][rounded_angle] = cv2.getRotationMatrix2D(center, rounded_angle, 1.0)

        return self.rotation_matrices[key][rounded_angle]

# High-performance rotation functions optimized for Intel 12th Gen
if NUMBA_AVAILABLE:
    @numba.jit(nopython=True, parallel=True, cache=True)
    def numba_rotate_bilinear(image, angle_rad, center_x, center_y):
        """Ultra-fast bilinear rotation using Numba JIT compilation"""
        height, width = image.shape[:2]
        cos_angle = math.cos(angle_rad)
        sin_angle = math.sin(angle_rad)

        rotated = np.zeros_like(image)

        for y in numba.prange(height):
            for x in numba.prange(width):
                # Inverse rotation to find source pixel
                src_x = cos_angle * (x - center_x) - sin_angle * (y - center_y) + center_x
                src_y = sin_angle * (x - center_x) + cos_angle * (y - center_y) + center_y

                # Check bounds
                if 0 <= src_x < width-1 and 0 <= src_y < height-1:
                    # Bilinear interpolation
                    x1, y1 = int(src_x), int(src_y)
                    x2, y2 = x1 + 1, y1 + 1

                    dx, dy = src_x - x1, src_y - y1

                    # Interpolate for each color channel
                    for c in range(image.shape[2]):
                        rotated[y, x, c] = (
                            image[y1, x1, c] * (1-dx) * (1-dy) +
                            image[y1, x2, c] * dx * (1-dy) +
                            image[y2, x1, c] * (1-dx) * dy +
                            image[y2, x2, c] * dx * dy
                        )

        return rotated

    @numba.jit(nopython=True, parallel=True, cache=True)
    def numba_rotate_nearest(image, angle_rad, center_x, center_y):
        """Ultra-fast nearest neighbor rotation using Numba JIT"""
        height, width = image.shape[:2]
        cos_angle = math.cos(angle_rad)
        sin_angle = math.sin(angle_rad)

        rotated = np.zeros_like(image)

        for y in numba.prange(height):
            for x in numba.prange(width):
                # Inverse rotation
                src_x = cos_angle * (x - center_x) - sin_angle * (y - center_y) + center_x
                src_y = sin_angle * (x - center_x) + cos_angle * (y - center_y) + center_y

                # Nearest neighbor
                src_x_int = int(src_x + 0.5)
                src_y_int = int(src_y + 0.5)

                if 0 <= src_x_int < width and 0 <= src_y_int < height:
                    rotated[y, x] = image[src_y_int, src_x_int]

        return rotated

# Threaded rotation for multi-core performance
class ThreadedRotationEngine:
    """Multi-threaded rotation engine optimized for Intel i5-1235U (10 cores)"""

    def __init__(self, num_threads=6):  # Leave 4 cores for system
        self.num_threads = num_threads
        self.thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=num_threads)

    def rotate_threaded(self, image, angle, interpolation=cv2.INTER_LINEAR):
        """Split image into chunks and rotate in parallel"""
        height, width = image.shape[:2]
        center = (width // 2, height // 2)
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)

        # Split image into horizontal strips for parallel processing
        chunk_height = height // self.num_threads
        futures = []

        for i in range(self.num_threads):
            start_y = i * chunk_height
            end_y = start_y + chunk_height if i < self.num_threads - 1 else height

            chunk = image[start_y:end_y]
            future = self.thread_pool.submit(
                self._rotate_chunk, chunk, rotation_matrix, width, interpolation, start_y
            )
            futures.append(future)

        # Combine results
        result = np.zeros_like(image)
        for i, future in enumerate(futures):
            start_y = i * chunk_height
            end_y = start_y + chunk_height if i < self.num_threads - 1 else height
            result[start_y:end_y] = future.result()

        return result

    def _rotate_chunk(self, chunk, rotation_matrix, full_width, interpolation, y_offset):
        """Rotate a chunk of the image"""
        chunk_height = chunk.shape[0]

        # Adjust rotation matrix for chunk offset
        adjusted_matrix = rotation_matrix.copy()
        adjusted_matrix[1, 2] -= y_offset  # Adjust for vertical offset

        rotated_chunk = cv2.warpAffine(
            chunk, adjusted_matrix, (full_width, chunk_height),
            flags=interpolation, borderMode=cv2.BORDER_CONSTANT, borderValue=(0, 0, 0)
        )

        return rotated_chunk

# External rotation using ImageMagick for ultimate quality
class ExternalRotationEngine:
    """External rotation using ImageMagick subprocess for maximum quality"""

    def __init__(self):
        self.temp_dir = Path("temp_rotation")
        self.temp_dir.mkdir(exist_ok=True)
        self.frame_cache = {}

    def check_imagemagick(self):
        """Check if ImageMagick is available"""
        try:
            result = subprocess.run(['magick', '-version'],
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False

    def rotate_with_imagemagick(self, image_path, angle, quality=95):
        """Rotate image using ImageMagick for maximum quality"""
        if not SUBPROCESS_AVAILABLE:
            return None

        try:
            output_path = self.temp_dir / f"rotated_{angle}_{quality}.jpg"

            cmd = [
                'magick', str(image_path),
                '-rotate', str(angle),
                '-quality', str(quality),
                '-strip',  # Remove metadata for smaller files
                str(output_path)
            ]

            result = subprocess.run(cmd, capture_output=True, timeout=10)
            if result.returncode == 0 and output_path.exists():
                return str(output_path)
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass

        return None

    def pregenerate_rotation_sequence(self, image_path, fps=60, duration=6.0):
        """Pre-generate rotation frames for ultra-smooth playback"""
        frames = []
        total_frames = int(fps * duration)
        angle_step = 360.0 / total_frames

        for i in range(total_frames):
            angle = i * angle_step
            frame_path = self.rotate_with_imagemagick(image_path, angle)
            if frame_path:
                frames.append(frame_path)

        return frames

# Optimized PhotoImage manager to prevent memory leaks
class PhotoImagePool:
    """Pool of reusable PhotoImage objects to prevent Tkinter memory leaks"""

    def __init__(self, pool_size=5):
        self.pool = []
        self.pool_size = pool_size
        self.current_index = 0

    def get_photo_image(self, image_array):
        """Get a PhotoImage from the pool, reusing existing objects"""
        try:
            # Convert numpy array to PIL Image
            if isinstance(image_array, np.ndarray):
                pil_image = Image.fromarray(image_array)
            else:
                pil_image = image_array

            # Reuse existing PhotoImage if available
            if len(self.pool) < self.pool_size:
                photo = ImageTk.PhotoImage(pil_image)
                self.pool.append(photo)
                return photo
            else:
                # Reuse existing PhotoImage by updating its data
                photo = self.pool[self.current_index]
                self.current_index = (self.current_index + 1) % self.pool_size

                # Update the existing PhotoImage
                try:
                    photo.paste(pil_image)
                except:
                    # Fallback: create new PhotoImage
                    photo = ImageTk.PhotoImage(pil_image)
                    self.pool[self.current_index] = photo

                return photo

        except Exception as e:
            print(f"PhotoImage pool error: {e}")
            # Fallback to direct creation
            return ImageTk.PhotoImage(pil_image)

class UltimateRotationEngine:
    """Ultimate high-performance rotation engine optimized for Intel 12th Gen i5-1235U"""

    def __init__(self, projection_window, projection_label, status_callback=None):
        self.projection_window = projection_window
        self.projection_label = projection_label
        self.status_callback = status_callback

        # Rotation state
        self.is_active = False
        self.current_angle = 0.0
        self.left_speed = 0.0  # 0-100
        self.right_speed = 0.0  # 0-100
        self.direction = 1  # 1 for left (CCW), -1 for right (CW)
        self.is_ping_pong = False

        # Performance settings optimized for Intel i5-1235U
        self.update_interval = 16  # milliseconds (60 FPS target)
        self.degrees_per_second_multiplier = 2.0
        self.max_fps = 90   # Conservative for 8GB RAM
        self.min_fps = 30   # Minimum FPS to maintain

        # Backend selection with auto-detection
        self.backend = self._detect_best_backend()
        self.adaptive_quality = True
        self.temporal_smoothing = False  # Disabled by default for performance
        self.quality_override = None

        # Animation control with precise timing
        self.after_id = None
        self.last_update_time = time.perf_counter()
        self._frame_skip_counter = 0
        self._target_frame_time = 1.0 / 60.0  # 60 FPS target

        # High-performance components
        self.buffer_manager = RotationBufferManager(max_width=1920, max_height=1080)
        self.photo_pool = PhotoImagePool(pool_size=3)  # Small pool for 8GB RAM

        # Initialize performance engines
        if NUMBA_AVAILABLE:
            self.threaded_engine = ThreadedRotationEngine(num_threads=4)  # Conservative threading
        else:
            self.threaded_engine = None

        self.external_engine = ExternalRotationEngine()

        # Image processing state
        self.base_image = None
        self.is_video_mode = False
        self._scaled_base_image = None
        self._scaled_cv_image = None
        self._scaled_dims = (0, 0)
        self._last_rendered_angle = None
        self._last_angle_time = time.perf_counter()

        # Reference to parent application
        self._parent_app = None

        # Performance monitoring
        self._frame_times = []
        self._performance_stats = {
            'avg_frame_time': 0.0,
            'dropped_frames': 0,
            'backend_switches': 0
        }

        # Initialize missing attributes for compatibility
        self._frame_history = []  # For temporal smoothing (disabled by default)
        self._max_history_frames = 3
        self._smoothing_factor = 0.15
        self._cached_cv_image = None  # Cache for converted images

        # Initialize old attributes that are still referenced (will be removed later)
        self._quality_cache = {}
        self._last_backend_used = None
        self._quality_fps_multiplier = 1.0

        # Auto-detect capabilities
        self._detect_system_capabilities()

    def _detect_best_backend(self):
        """Auto-detect the best rotation backend for this system"""
        if NUMBA_AVAILABLE:
            print("🚀 Using NUMBA backend for maximum performance")
            return RotationBackend.NUMBA
        else:
            print("🔧 Using BUFFERED backend with pre-allocated memory")
            return RotationBackend.BUFFERED

    def _detect_system_capabilities(self):
        """Detect system capabilities and optimize settings"""
        # Check available memory
        try:
            import psutil
            memory_gb = psutil.virtual_memory().total / (1024**3)
            if memory_gb < 8:
                self.max_fps = 60  # Lower FPS for systems with less RAM
                self.photo_pool = PhotoImagePool(pool_size=2)
            print(f"💾 Detected {memory_gb:.1f}GB RAM - optimizing settings")
        except ImportError:
            print("⚠️ psutil not available - using conservative settings")

        # Check ImageMagick availability
        if self.external_engine.check_imagemagick():
            print("🎨 ImageMagick detected - external rotation available")
        else:
            print("⚠️ ImageMagick not found - using internal rotation only")

    def _get_optimal_quality(self, speed: float) -> RotationQuality:
        """Determine optimal quality based on rotation speed and system performance"""
        if self.quality_override:
            return self.quality_override

        if not self.adaptive_quality:
            return RotationQuality.HIGH

        # Adaptive quality based on speed and system performance
        avg_frame_time = self._performance_stats['avg_frame_time']

        # If we're dropping frames, reduce quality
        if avg_frame_time > self._target_frame_time * 1.5:
            if speed <= 25:
                return RotationQuality.FAST
            else:
                return RotationQuality.FASTEST

        # Normal quality selection based on speed
        if speed <= 15:
            return RotationQuality.HIGH      # Slow - high quality
        elif speed <= 40:
            return RotationQuality.MEDIUM    # Medium - balanced
        elif speed <= 70:
            return RotationQuality.FAST      # Fast - performance focused
        else:
            return RotationQuality.FASTEST   # Very fast - maximum performance

    def _high_performance_rotate(self, image: np.ndarray, angle: float, quality: RotationQuality) -> np.ndarray:
        """High-performance rotation using the best available backend"""
        height, width = image.shape[:2]

        # Performance monitoring
        start_time = time.perf_counter()

        try:
            if self.backend == RotationBackend.NUMBA and NUMBA_AVAILABLE:
                # Use Numba JIT-compiled rotation
                angle_rad = math.radians(angle)
                center_x, center_y = width // 2, height // 2

                if quality == RotationQuality.FASTEST:
                    rotated = numba_rotate_nearest(image, angle_rad, center_x, center_y)
                else:
                    rotated = numba_rotate_bilinear(image, angle_rad, center_x, center_y)

            elif self.backend == RotationBackend.THREADED and self.threaded_engine:
                # Use multi-threaded rotation
                interpolation = cv2.INTER_LINEAR if quality != RotationQuality.FASTEST else cv2.INTER_NEAREST
                rotated = self.threaded_engine.rotate_threaded(image, angle, interpolation)

            elif self.backend == RotationBackend.BUFFERED:
                # Use pre-allocated buffers
                buffers = self.buffer_manager.get_buffers(width, height)
                rotation_matrix = self.buffer_manager.get_rotation_matrix(width, height, angle)

                # Select interpolation
                interpolation_map = {
                    RotationQuality.HIGH: cv2.INTER_LANCZOS4,
                    RotationQuality.MEDIUM: cv2.INTER_CUBIC,
                    RotationQuality.FAST: cv2.INTER_LINEAR,
                    RotationQuality.FASTEST: cv2.INTER_NEAREST
                }
                interpolation = interpolation_map.get(quality, cv2.INTER_LINEAR)

                # Rotate using pre-allocated buffer
                cv2.warpAffine(
                    image, rotation_matrix, (width, height),
                    dst=buffers['output'],
                    flags=interpolation,
                    borderMode=cv2.BORDER_CONSTANT,
                    borderValue=(0, 0, 0)
                )
                rotated = buffers['output'].copy()  # Copy to avoid buffer reuse issues

            else:
                # Fallback to standard OpenCV
                rotated = self._opencv_rotate_fallback(image, angle, quality)

        except Exception as e:
            print(f"⚠️ High-performance rotation failed: {e}, falling back to OpenCV")
            rotated = self._opencv_rotate_fallback(image, angle, quality)

        # Update performance stats
        frame_time = time.perf_counter() - start_time
        self._update_performance_stats(frame_time)

        return rotated

    def _opencv_rotate_fallback(self, image: np.ndarray, angle: float, quality: RotationQuality) -> np.ndarray:
        """Fallback OpenCV rotation method"""
        height, width = image.shape[:2]
        center = (width / 2, height / 2)
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)

        # Select interpolation method based on quality
        interpolation_map = {
            RotationQuality.HIGH: cv2.INTER_LANCZOS4,
            RotationQuality.MEDIUM: cv2.INTER_CUBIC,
            RotationQuality.FAST: cv2.INTER_LINEAR,
            RotationQuality.FASTEST: cv2.INTER_NEAREST
        }

        interpolation = interpolation_map.get(quality, cv2.INTER_LINEAR)

        # Perform rotation
        rotated = cv2.warpAffine(
            image, rotation_matrix, (width, height),
            flags=interpolation,
            borderMode=cv2.BORDER_CONSTANT,
            borderValue=(0, 0, 0)
        )

        return rotated

    def _update_performance_stats(self, frame_time: float):
        """Update performance statistics for adaptive optimization"""
        self._frame_times.append(frame_time)

        # Keep only recent frame times (last 60 frames)
        if len(self._frame_times) > 60:
            self._frame_times.pop(0)

        # Calculate average frame time
        if self._frame_times:
            self._performance_stats['avg_frame_time'] = sum(self._frame_times) / len(self._frame_times)

        # Check for dropped frames
        if frame_time > self._target_frame_time * 1.5:
            self._performance_stats['dropped_frames'] += 1

    def _pil_rotate(self, image: Image.Image, angle: float, quality: RotationQuality) -> Image.Image:
        """High-quality PIL rotation with advanced resampling"""
        # Select resampling method based on quality
        resampling_map = {
            RotationQuality.ULTRA: Image.Resampling.LANCZOS,
            RotationQuality.HIGH: Image.Resampling.LANCZOS,
            RotationQuality.MEDIUM: Image.Resampling.BICUBIC,
            RotationQuality.FAST: Image.Resampling.BILINEAR,
            RotationQuality.FASTEST: Image.Resampling.NEAREST
        }

        resampling = resampling_map[quality]

        # For ultra quality, use multi-pass rotation for large angles
        if quality == RotationQuality.ULTRA and abs(angle) > 45:
            # Break large rotations into smaller steps for better quality
            steps = max(2, int(abs(angle) / 45))
            step_angle = angle / steps
            rotated_image = image

            for _ in range(steps):
                rotated_image = rotated_image.rotate(
                    step_angle,
                    resample=resampling,
                    expand=False,
                    fillcolor=(0, 0, 0)
                )
            return rotated_image
        else:
            # Single-pass rotation
            return image.rotate(
                angle,
                resample=resampling,
                expand=False,
                fillcolor=(0, 0, 0)
            )

    def _apply_temporal_smoothing(self, current_frame: np.ndarray) -> np.ndarray:
        """Apply temporal smoothing for ultra-smooth rotation"""
        if not self.temporal_smoothing or len(self._frame_history) == 0:
            # Add to history and return current frame
            self._frame_history.append(current_frame.copy())
            if len(self._frame_history) > self._max_history_frames:
                self._frame_history.pop(0)
            return current_frame

        # Blend current frame with recent history
        blended = current_frame.astype(np.float32)
        total_weight = 1.0

        for i, historical_frame in enumerate(reversed(self._frame_history)):
            weight = self._smoothing_factor * (0.5 ** i)  # Exponential decay
            blended += historical_frame.astype(np.float32) * weight
            total_weight += weight

        # Normalize and convert back
        blended = (blended / total_weight).astype(np.uint8)

        # Update history
        self._frame_history.append(current_frame.copy())
        if len(self._frame_history) > self._max_history_frames:
            self._frame_history.pop(0)

        return blended

    def start_rotation(self, left_speed=0.0, right_speed=0.0, image_path=None):
        """Start ultimate rotation with adaptive quality and performance optimization"""
        self.stop_rotation()

        self.left_speed = max(0.0, min(100.0, float(left_speed)))
        self.right_speed = max(0.0, min(100.0, float(right_speed)))

        # No rotation if both speeds are 0
        if self.left_speed <= 0 and self.right_speed <= 0:
            return False

        # Determine mode
        if self.left_speed > 0 and self.right_speed > 0:
            # Ping-pong mode: start with higher speed direction
            self.is_ping_pong = True
            if self.left_speed >= self.right_speed:
                self.direction = 1  # Start left
            else:
                self.direction = -1  # Start right
        elif self.left_speed > 0:
            # Continuous left
            self.is_ping_pong = False
            self.direction = 1
        else:
            # Continuous right
            self.is_ping_pong = False
            self.direction = -1

        # Load base image if provided (for image rotation)
        if image_path:
            try:
                self.base_image = Image.open(image_path)
                self.is_video_mode = False
            except Exception as e:
                print(f"Failed to load image for rotation: {e}")
                return False
        else:
            # Video mode – no base image, angle will be calculated per frame
            self.is_video_mode = True
            self.base_image = None

        # Advanced FPS calculation based on speed, quality, and 4K/1080p mode
        max_speed = max(self.left_speed, self.right_speed)
        quality = self._get_optimal_quality(max_speed)

        # Get quality mode from parent app for FPS optimization
        quality_mode = "4K"  # Default
        if self._parent_app and hasattr(self._parent_app, 'quality_mode'):
            quality_mode = self._parent_app.quality_mode

        # Update FPS settings based on quality mode if needed
        if quality_mode == "4K" and self.max_fps < 60:
            self.update_quality_fps_settings("4K")
        elif quality_mode == "1080p" and self.max_fps != 90:
            self.update_quality_fps_settings("1080p")

        # More conservative FPS calculation
        if quality == RotationQuality.ULTRA:
            base_target = 40 + (max_speed / 100.0) * 40  # 40-80 base range
        elif quality == RotationQuality.HIGH:
            base_target = 30 + (max_speed / 100.0) * 30  # 30-60 base range
        elif quality == RotationQuality.MEDIUM:
            base_target = 20 + (max_speed / 100.0) * 25  # 20-45 base range
        else:
            base_target = 15 + (max_speed / 100.0) * 20  # 15-35 base range

        # Apply quality multiplier and clamp to safe ranges
        target_fps = min(self.max_fps, max(self.min_fps, base_target))

        # Quality-specific minimum intervals for stability
        if quality_mode == "1080p":
            # 1080p: Minimum 50ms interval (20 FPS max) for stability
            self.update_interval = max(50, int(1000 / target_fps))
        else:
            # 4K: Minimum 16ms interval (60 FPS max) for performance
            self.update_interval = max(16, int(1000 / target_fps))

        self.is_active = True
        self.current_angle = 0.0
        self.last_update_time = time.perf_counter()

        # Reset caches and buffers for new rotation session
        self._scaled_base_image = None
        self._scaled_dims = (0, 0)
        self._rotation_canvas = None
        self._frame_history.clear()  # Clear temporal smoothing history

        # Track backend changes for performance monitoring
        if self._last_backend_used != self.backend:
            self._performance_stats['backend_switches'] += 1
            self._last_backend_used = self.backend

        # For image mode we still need the Tk update loop; video mode computes angle on demand
        if not self.is_video_mode:
            self._update_rotation()

        # Reset per-frame clock for video mode
        self._last_angle_time = time.perf_counter()

        # Log rotation start with quality info
        quality = self._get_optimal_quality(max_speed)
        mode_desc = "ping-pong" if self.is_ping_pong else ("left" if self.direction == 1 else "right")

        # Get quality mode for display
        quality_mode = "4K"
        if self._parent_app and hasattr(self._parent_app, 'quality_mode'):
            quality_mode = self._parent_app.quality_mode

        if self.status_callback:
            self.status_callback(f"🔄 Ultimate rotation started - Mode: {mode_desc.upper()}, {quality_mode} Quality: {quality.value.upper()}, FPS: {int(1000/self.update_interval)}")

        return True

    def stop_rotation(self):
        """Stop rotation animation and reset all rotation state"""
        if self.after_id and self.projection_window and self.projection_window.winfo_exists():
            try:
                self.projection_window.after_cancel(self.after_id)
            except:
                pass

        self.after_id = None
        self.is_active = False
        self.current_angle = 0.0
        self.left_speed = 0.0
        self.right_speed = 0.0
        self.is_video_mode = False
        self._last_rendered_angle = None

        # Clear cached images and reset state
        self.base_image = None
        self._scaled_base_image = None
        self._scaled_cv_image = None
        self._cached_cv_image = None
        self._frame_history.clear()

        # Reset performance stats
        self._frame_times.clear()
        self._performance_stats['dropped_frames'] = 0

        if self.status_callback:
            self.status_callback("🔄 Rotation stopped - all settings reset")

    def _update_rotation(self):
        """Update rotation angle with precise timing and frame skipping"""
        if not self.is_active or self.is_video_mode:
            return

        current_time = time.perf_counter()

        # Check if we should skip this frame to maintain target FPS
        time_since_last = current_time - self.last_update_time
        if time_since_last < self._target_frame_time * 0.8:  # Allow some variance
            # Schedule next update sooner
            self.after_id = self.projection_window.after(5, self._update_rotation)
            return

        self._advance_angle()

        # Update display if we have an image
        if self.base_image:
            try:
                self._update_image_display_optimized()
            except Exception as e:
                print(f"⚠️ Display update failed: {e}")
                # Fallback to basic display
                self._update_image_display_fallback()

        # Adaptive scheduling based on performance
        if self.projection_window and self.projection_window.winfo_exists():
            # Calculate next update interval based on performance
            avg_frame_time = self._performance_stats['avg_frame_time']

            if avg_frame_time > self._target_frame_time * 1.2:
                # We're running slow, increase interval
                next_interval = max(20, int(self.update_interval * 1.2))
                self._frame_skip_counter += 1
            elif avg_frame_time < self._target_frame_time * 0.8:
                # We're running fast, decrease interval
                next_interval = max(8, int(self.update_interval * 0.9))
            else:
                # Running at target speed
                next_interval = self.update_interval

            self.after_id = self.projection_window.after(next_interval, self._update_rotation)

    # ------------------------------------------------------------------
    # Shared math helpers
    # ------------------------------------------------------------------
    def _advance_angle(self):
        """Advance current_angle based on elapsed real time."""
        current_time = time.perf_counter()
        delta_time = current_time - self.last_update_time
        self.last_update_time = current_time

        # Calculate rotation speed based on current direction
        if self.is_ping_pong:
            current_speed = self.left_speed if self.direction == 1 else self.right_speed
        else:
            current_speed = self.left_speed if self.direction == 1 else self.right_speed

        # Convert speed to degrees per second and apply
        degrees_per_second = current_speed * self.degrees_per_second_multiplier
        angle_delta = degrees_per_second * delta_time * self.direction
        self.current_angle += angle_delta

        # Handle ping-pong direction changes (every 180 degrees)
        if self.is_ping_pong:
            if abs(self.current_angle) >= 180:
                self.direction *= -1  # Reverse direction
                # Reset angle to continue from the boundary
                if self.current_angle > 0:
                    self.current_angle = 180 - (self.current_angle - 180)
                else:
                    self.current_angle = -180 - (self.current_angle + 180)
                print(f"🔄 Ping-pong direction change: now {'left' if self.direction == 1 else 'right'} at {self.current_angle:.1f}°")

        # Keep angle in reasonable range
        if self.current_angle > 360 or self.current_angle < -360:
            self.current_angle = self.current_angle % 360

    def _update_image_display_optimized(self):
        """Optimized image display with memory pooling and performance monitoring"""
        if not self.base_image or not self.projection_label:
            return

        # Skip redraw if the visual change would be imperceptible
        angle_threshold = 0.2 if self._performance_stats['avg_frame_time'] > self._target_frame_time else 0.1
        if self._last_rendered_angle is not None and abs(self.current_angle - self._last_rendered_angle) < angle_threshold:
            return  # No significant change – save CPU

        # Get current window size
        window_width = self.projection_window.winfo_width()
        window_height = self.projection_window.winfo_height()

        # Get fill setting for current cue
        fill_screen = False
        if (self._parent_app and
            0 <= self._parent_app.current_cue_index < len(self._parent_app.cue_list)):
            fill_screen = self._parent_app.cue_list[self._parent_app.current_cue_index].get('fill', False)

        # Process image with high-performance pipeline
        processed_image = self._process_image_frame_optimized(window_width, window_height, fill_screen)

        if processed_image is not None:
            # Use photo pool to prevent memory leaks
            photo = self.photo_pool.get_photo_image(processed_image)
            self.projection_label.configure(image=photo)
            self.projection_label.image = photo  # Keep reference

        # Remember angle rendered
        self._last_rendered_angle = self.current_angle

    def _update_image_display_fallback(self):
        """Fallback display method for error recovery"""
        try:
            window_width = self.projection_window.winfo_width()
            window_height = self.projection_window.winfo_height()
            self._show_base_image_fallback(window_width, window_height)
        except Exception as e:
            print(f"❌ Even fallback display failed: {e}")

    def _process_image_frame_optimized(self, window_width: int, window_height: int, fill_screen=False):
        """Optimized image processing with high-performance rotation"""
        try:
            # Convert PIL image to OpenCV format (BGR) - cached conversion
            if not hasattr(self, '_cached_cv_image') or self._cached_cv_image is None:
                self._cached_cv_image = cv2.cvtColor(np.array(self.base_image), cv2.COLOR_RGB2BGR)

            frame = self._cached_cv_image

            # Apply quality-based resolution limits
            quality_settings = self._parent_app.quality_settings[self._parent_app.quality_mode] if self._parent_app else {"max_width": 1920, "max_height": 1080}
            max_width = quality_settings["max_width"]
            max_height = quality_settings["max_height"]

            # Limit the target resolution based on quality mode
            if not fill_screen:
                if window_width > max_width or window_height > max_height:
                    scale_factor = min(max_width / window_width, max_height / window_height)
                    window_width = int(window_width * scale_factor)
                    window_height = int(window_height * scale_factor)
            else:
                window_width = min(window_width, max_width)
                window_height = min(window_height, max_height)

            # BGR->RGB for PIL/Tk compatibility
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # Pre-scale original frame to fit/fill the projection window
            in_h, in_w = frame_rgb.shape[:2]
            if fill_screen:
                target_w, target_h = window_width, window_height
            else:
                frame_ratio = in_w / in_h
                window_ratio = window_width / window_height
                if frame_ratio > window_ratio:
                    target_w = window_width
                    target_h = int(window_width / frame_ratio)
                else:
                    target_h = window_height
                    target_w = int(window_height * frame_ratio)

            target_w = max(1, target_w)
            target_h = max(1, target_h)
            if (in_w, in_h) != (target_w, target_h):
                frame_rgb = cv2.resize(frame_rgb, (target_w, target_h), cv2.INTER_AREA)

            # Apply high-performance rotation FIRST (rotate the image in place)
            if self.is_active:
                quality = self._get_optimal_quality(max(self.left_speed, self.right_speed))
                frame_rgb = self._high_performance_rotate(frame_rgb, self.current_angle, quality)

            # Now place the rotated image onto the final canvas for display
            # Create canvas sized to the window
            canvas = np.zeros((window_height, window_width, 3), dtype=frame_rgb.dtype)

            # Center the rotated image on the canvas
            canvas_h, canvas_w = canvas.shape[:2]
            img_h, img_w = frame_rgb.shape[:2]

            # Calculate position to center the image
            y_offset = max(0, (canvas_h - img_h) // 2)
            x_offset = max(0, (canvas_w - img_w) // 2)

            # Calculate how much of the image fits
            y_end = min(canvas_h, y_offset + img_h)
            x_end = min(canvas_w, x_offset + img_w)
            img_y_end = min(img_h, canvas_h - y_offset)
            img_x_end = min(img_w, canvas_w - x_offset)

            # Place the rotated image centered on the canvas
            if img_y_end > 0 and img_x_end > 0:
                canvas[y_offset:y_end, x_offset:x_end] = frame_rgb[:img_y_end, :img_x_end]

            cropped = canvas

            # Return numpy array for photo pool processing
            return cropped

        except Exception as e:
            print(f"Error in optimized image processing: {e}")
            return None










    def _show_base_image_fallback(self, window_width: int, window_height: int):
        """Ultimate fallback - just show the base image without rotation"""
        try:
            # Simple resize and display
            img_width, img_height = self.base_image.size
            scale = min(window_width / img_width, window_height / img_height)
            new_width = max(1, int(img_width * scale))
            new_height = max(1, int(img_height * scale))

            resized_image = self.base_image.resize((new_width, new_height), Image.Resampling.BILINEAR)

            # Center on black background
            final_image = Image.new('RGB', (window_width, window_height), (0, 0, 0))
            paste_x = (window_width - new_width) // 2
            paste_y = (window_height - new_height) // 2
            final_image.paste(resized_image, (paste_x, paste_y))

            photo = ImageTk.PhotoImage(final_image)
            self.projection_label.configure(image=photo)
            self.projection_label.image = photo

            print("🔄 Showing base image without rotation (fallback mode)")

        except Exception as e:
            print(f"❌ Even base image fallback failed: {e}")



    def _update_photoimage(self, rotated_image: Image.Image, window_width: int, window_height: int):
        """Optimized PhotoImage update with memory management"""
        # Reuse a single PhotoImage object each frame to avoid Tk image handle exhaustion
        if self._photo is None or self._photo.width() != window_width or self._photo.height() != window_height:
            # First frame or size change – create the PhotoImage
            self._photo = ImageTk.PhotoImage(rotated_image)
            self.projection_label.configure(image=self._photo)
        else:
            # Subsequent frames – replace pixel data in-place (zero allocations on Tk side)
            try:
                self._photo.paste(rotated_image)
            except Exception:
                # Fallback to new PhotoImage if paste fails for any reason
                self._photo = ImageTk.PhotoImage(rotated_image)
                self.projection_label.configure(image=self._photo)

        # Keep reference to prevent garbage collection
        self.projection_label.image = self._photo

    def get_current_angle(self):
        """Get current rotation angle; for video mode, compute on the fly for max smoothness."""
        if not self.is_active:
            return 0.0

        if self.is_video_mode:
            # Advance angle using elapsed time since last query
            self._advance_angle()
        return self.current_angle

    def set_backend(self, backend: RotationBackend):
        """Change rotation backend with validation"""
        old_backend = self.backend

        # Validate backend availability
        if backend == RotationBackend.NUMBA and not NUMBA_AVAILABLE:
            print("⚠️ Numba not available, falling back to BUFFERED backend")
            backend = RotationBackend.BUFFERED
        elif backend == RotationBackend.THREADED and not self.threaded_engine:
            print("⚠️ Threading not available, falling back to BUFFERED backend")
            backend = RotationBackend.BUFFERED
        elif backend == RotationBackend.EXTERNAL and not self.external_engine.check_imagemagick():
            print("⚠️ ImageMagick not available, falling back to BUFFERED backend")
            backend = RotationBackend.BUFFERED

        self.backend = backend

        if old_backend != backend:
            self._performance_stats['backend_switches'] += 1
            print(f"🔄 Rotation backend changed: {old_backend.value} → {backend.value}")

    def set_quality_override(self, quality: Optional[RotationQuality]):
        """Override automatic quality selection"""
        self.quality_override = quality
        print(f"🎨 Quality override set: {quality.value if quality else 'Auto'}")

    def set_temporal_smoothing(self, enabled: bool):
        """Enable/disable temporal smoothing (disabled by default for performance)"""
        self.temporal_smoothing = enabled
        if not enabled:
            self._frame_history.clear()
        print(f"🎬 Temporal smoothing: {'Enabled' if enabled else 'Disabled'}")

    def get_performance_info(self) -> str:
        """Get human-readable performance information"""
        stats = self._performance_stats
        backend_name = self.backend.value.upper()

        if stats['avg_frame_time'] > 0:
            current_fps = 1.0 / stats['avg_frame_time']
            fps_status = "🟢" if current_fps >= 50 else "🟡" if current_fps >= 30 else "🔴"
        else:
            current_fps = 0
            fps_status = "⚪"

        return f"{fps_status} {backend_name} | {current_fps:.1f} FPS | Dropped: {stats['dropped_frames']}"

    def _apply_temporal_smoothing(self, current_frame: np.ndarray) -> np.ndarray:
        """Apply temporal smoothing for ultra-smooth rotation (disabled by default for performance)"""
        if not self.temporal_smoothing or len(self._frame_history) == 0:
            # Add to history and return current frame
            self._frame_history.append(current_frame.copy())
            if len(self._frame_history) > self._max_history_frames:
                self._frame_history.pop(0)
            return current_frame

        # Blend current frame with recent history
        blended = current_frame.astype(np.float32)
        total_weight = 1.0

        for i, historical_frame in enumerate(reversed(self._frame_history)):
            weight = self._smoothing_factor * (0.5 ** i)  # Exponential decay
            blended += historical_frame.astype(np.float32) * weight
            total_weight += weight

        # Normalize and convert back
        blended = (blended / total_weight).astype(np.uint8)

        # Update history
        self._frame_history.append(current_frame.copy())
        if len(self._frame_history) > self._max_history_frames:
            self._frame_history.pop(0)

        return blended

    def update_quality_fps_settings(self, quality_mode: str):
        """Update FPS settings based on quality mode (optimized for Intel i5-1235U)"""
        if quality_mode == "4K":
            # 4K mode: Conservative settings for 8GB RAM
            self.max_fps = 60   # Conservative for 4K on 8GB RAM
            self.min_fps = 30   # Maintain smooth playback
            print(f"🎬 Rotation optimized for 4K: Max {self.max_fps} FPS, Min {self.min_fps} FPS")
        else:  # 1080p
            # 1080p mode: Higher performance possible
            self.max_fps = 90   # Take advantage of better performance at 1080p
            self.min_fps = 30   # Maintain smooth playback
            print(f"🎬 Rotation optimized for 1080p: Max {self.max_fps} FPS, Min {self.min_fps} FPS")

        # Update target frame time
        self._target_frame_time = 1.0 / 60.0  # Always target 60 FPS

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        max_speed = max(self.left_speed, self.right_speed)
        quality = self._get_optimal_quality(max_speed)

        return {
            'active': self.is_active,
            'current_angle': self.current_angle,
            'left_speed': self.left_speed,
            'right_speed': self.right_speed,
            'quality': quality.value,
            'backend': self.backend.value,
            'fps': int(1000 / self.update_interval) if self.update_interval > 0 else 0,
            'temporal_smoothing': self.temporal_smoothing,
            'cache_size': len(self._rotation_matrix_cache),
            'frame_history_size': len(self._frame_history)
        }

# Ultimate rotation system implemented - maximum performance and quality
















def _debug_filtered_print(*args, **kwargs):
    """Filter out internal debug prints marked with the "🔍 DEBUG" prefix (or other known debug strings)."""
    if args and isinstance(args[0], str):
        first_arg = args[0]
        if '🔍 DEBUG' in first_arg or first_arg.startswith('Simple transition'):
            return  # Skip debug output
    _original_print(*args, **kwargs)

_builtins.print = _debug_filtered_print

# === SIMPLE FADE SYSTEM ===

class SimpleFadeSystem:
    """Extremely simple fade manager that strictly follows the user specification.

    Behaviour:
    1. fade_out_then_switch(sec, cb):
       • If sec <= 0 → cb() immediately.
       • Otherwise, overlay starts fully transparent and fades to black over <sec> seconds.
         Once fully black, cb() is executed while the screen remains black.
    2. fade_in_after_switch(sec, cb):
       • If sec <= 0 → cb() immediately.
       • Otherwise, an instant black overlay is shown, cb() is executed behind it, then
         the overlay fades to transparent over <sec> seconds and is destroyed.
    """

    def __init__(self, projection_window=None, projection_label=None):
        self.projection_window = projection_window
        self.projection_label = projection_label
        self.overlay = None  # The black overlay window
        self._after_id = None  # Reference to the scheduled after() callback
        # Track Configure event binding so we can unbind later
        self._resize_binding = None  # type: Optional[str]
        
    # ------------------------------------------------------------------
    # Utility helpers
    # ------------------------------------------------------------------
    def _update_overlay_geometry(self):
        """Update overlay geometry to match the projection window. Triggered by <Configure> events."""
        if (self.overlay and self.overlay.winfo_exists() and
                self.projection_window and self.projection_window.winfo_exists()):
            try:
                x, y = self.projection_window.winfo_rootx(), self.projection_window.winfo_rooty()
                w, h = self.projection_window.winfo_width(), self.projection_window.winfo_height()
                self.overlay.geometry(f"{w}x{h}+{x}+{y}")
            except Exception:
                pass

    def _create_overlay(self, alpha: float):
        """Create a full-size black overlay on top of the projection window."""
        # Destroy existing overlay if still present
        if self.overlay and self.overlay.winfo_exists():
            self.overlay.destroy()
            self.overlay = None

        # Remove any existing resize binding
        if self._resize_binding and self.projection_window:
            try:
                self.projection_window.unbind('<Configure>', self._resize_binding)
            except Exception:
                pass
            self._resize_binding = None

        pw = self.projection_window
        if not pw or not pw.winfo_exists():
            return  # No projection window available

        # Use root coordinates so the overlay aligns with window decorations precisely
        x, y = pw.winfo_rootx(), pw.winfo_rooty()
        w, h = pw.winfo_width(), pw.winfo_height()

        self.overlay = tk.Toplevel(pw)
        self.overlay.overrideredirect(True)
        self.overlay.attributes('-topmost', True)
        self.overlay.configure(bg='black')
        self.overlay.attributes('-alpha', alpha)
        self.overlay.geometry(f"{w}x{h}+{x}+{y}")

        # Bind a single <Configure> handler so geometry is only updated on real resizes/moves
        try:
            self._resize_binding = self.projection_window.bind(
                '<Configure>', lambda e: self._update_overlay_geometry())
        except Exception:
            self._resize_binding = None

    def _cancel_animation(self):
        if self._after_id and self.projection_window and self.projection_window.winfo_exists():
            try:
                self.projection_window.after_cancel(self._after_id)
            except Exception:
                pass
        self._after_id = None

    # ------------------------------------------------------------------
    # Public API
    # ------------------------------------------------------------------
    def stop_fade(self):
        """Immediately stop any ongoing fade and remove the overlay."""
        self._cancel_animation()
        if self.overlay and self.overlay.winfo_exists():
            self.overlay.destroy()
        self.overlay = None
        # Clean up resize binding
        if self._resize_binding and self.projection_window:
            try:
                self.projection_window.unbind('<Configure>', self._resize_binding)
            except Exception:
                pass
        self._resize_binding = None

    def fade_out_then_switch(self, seconds: float, switch_callback):
        """Fade from transparent to black, then execute *switch_callback*.

        If *seconds* is 0 or less the switch happens instantly with no fade.
        """
        if seconds <= 0 or not self.projection_window or not self.projection_window.winfo_exists():
            switch_callback()
            return

        self.stop_fade()
        self._create_overlay(alpha=0.0)

        total_steps = max(int(seconds * 30), 1)  # ~30 FPS
        step_ms = int((seconds * 1000) / total_steps)

        def _step(step=0):
            if not self.overlay or not self.overlay.winfo_exists():
                return  # Overlay vanished, abort

            alpha = min(1.0, step / total_steps)
            self.overlay.attributes('-alpha', alpha)

            if step >= total_steps:
                # Fully black – perform the switch while screen is black
                switch_callback()
                return

            self._after_id = self.projection_window.after(step_ms, lambda: _step(step + 1))

        _step()

    def fade_in_after_switch(self, seconds: float, content_callback):
        """Show black screen, run *content_callback*, then fade back to transparent."""
        if seconds <= 0 or not self.projection_window or not self.projection_window.winfo_exists():
            content_callback()
            return

        self._cancel_animation()
        # Reuse existing overlay if present; otherwise create a new one
        if self.overlay and self.overlay.winfo_exists():
            self.overlay.attributes('-alpha', 1.0)
        else:
            self._create_overlay(alpha=1.0)  # Start fully black

        # Load new content behind the black overlay
        content_callback()

        total_steps = max(int(seconds * 30), 1)
        step_ms = int((seconds * 1000) / total_steps)

        def _step(step=0):
            if not self.overlay or not self.overlay.winfo_exists():
                return  # Overlay vanished, abort

            alpha = max(0.0, 1.0 - (step / total_steps))
            self.overlay.attributes('-alpha', alpha)

            if step >= total_steps:
                # Fade finished – remove overlay
                if self.overlay and self.overlay.winfo_exists():
                    self.overlay.destroy()
                self.overlay = None
                # Clean up resize binding now that overlay is gone
                if self._resize_binding and self.projection_window:
                    try:
                        self.projection_window.unbind('<Configure>', self._resize_binding)
                    except Exception:
                        pass
                    self._resize_binding = None
                return

            self._after_id = self.projection_window.after(step_ms, lambda: _step(step + 1))

        _step()

    def _sync_overlay_geometry(self):
        """Ensure the overlay always covers the projection window (handles resizing)."""
        if (self.overlay and self.overlay.winfo_exists() and
                self.projection_window and self.projection_window.winfo_exists()):
            try:
                self.projection_window.update_idletasks()
                x, y = self.projection_window.winfo_rootx(), self.projection_window.winfo_rooty()
                w, h = self.projection_window.winfo_width(), self.projection_window.winfo_height()
                self.overlay.geometry(f"{w}x{h}+{x}+{y}")
            except Exception:
                pass

# === END SIMPLE FADE SYSTEM ===

class TheaterProjection:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Theater Projection Control")

        # Set minimum window size and make it resizable
        self.root.minsize(800, 650)  # Minimum usable size - increased to show all controls
        self.root.geometry("1200x800")  # Default size - increased height to ensure playback controls are visible
        self.root.configure(bg='#2b2b2b')

        # Make window resizable
        self.root.resizable(True, True)
        
        # Projection window reference
        self.projection_window = None
        self.projection_label = None
        
        # Store current projection window size for dynamic scaling
        self.projection_size: Tuple[int, int] = (1024, 768)
        
        # SIMPLE FADE SYSTEM
        self.fade_system = SimpleFadeSystem()
        
        # Media handling
        self.cue_list = []
        self.current_cue_index = -1
        self.current_video_cap = None
        self.video_thread = None
        self.is_playing = False
        
        # File name display limit
        self.max_filename_length = 25  # Reduced for table view
        

        
        # Multi-selection variables
        self.last_selected_index = None
        
        # Undo/Redo system
        self.cue_history = []
        self.history_index = -1
        self.clipboard = None
        
        # Keybind system - always enabled (no toggle)
        self.keybinds_enabled = True
        self.custom_keybinds = {
            'up_cue': '<Up>',
            'down_cue': '<Down>',
            'play': '<space>',
            'stop': '<Escape>'
        }

        # UI control references for enabling/disabling
        self.edit_controls = []
        self.playback_controls = []

        # Supported file formats
        self.supported_formats = {
            'images': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'],
            'videos': ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
        }

        # Media cache and preloading system
        self.media_cache = {}  # Cache for processed images
        self.preload_queue = queue.Queue()
        self.preload_executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="MediaPreloader")
        self.preload_active = False
        self.cache_size_limit = 50  # Maximum cached items
        self.preload_distance = 2  # How many cues ahead to preload

        # Video optimization
        self.video_frame_buffer = queue.Queue(maxsize=5)  # Buffer for smooth video playback
        self.video_buffer_thread = None
        self.target_fps = 30  # Target FPS for video playback

        # Quality settings
        self.quality_mode = "4K"  # Default to 4K quality
        self.quality_settings = {
            "4K": {"max_width": 3840, "max_height": 2160, "resampling": "LANCZOS"},
            "1080p": {"max_width": 1920, "max_height": 1080, "resampling": "BICUBIC"}
        }

        # === Enhanced Image Rotation System ===
        # New high-performance rotation engine
        self.rotation_engine = None  # Will be initialized after projection window is created
        # === End enhanced rotation system ===

        self.setup_ui()

        # Initialize undo system with empty state
        self.save_state_for_undo()

        # Setup keybinds after UI is created
        self.setup_keybinds()

        # Bind window resize event for responsive behavior
        self.root.bind('<Configure>', self.on_window_resize)

        self._custom_fullscreen = False
        self._pre_fullscreen_geometry = None
        self._using_native_fullscreen = False
        self._current_display_index = 0  # Track which display we're using
        self._available_displays = []    # Cache of available displays

        # Initialize display detection on startup
        self._initialize_displays()

    def _update_performance_display(self):
        """Update the performance indicator in the status bar"""
        try:
            if self.rotation_engine and self.rotation_engine.is_active:
                perf_info = self.rotation_engine.get_performance_info()
                self.perf_var.set(perf_info)
            else:
                # Show system readiness when not rotating
                backend = "READY"
                if hasattr(self, 'rotation_engine') and self.rotation_engine:
                    backend = self.rotation_engine.backend.value.upper()
                self.perf_var.set(f"⚪ {backend}")
        except Exception:
            self.perf_var.set("⚪ READY")

        # Schedule next update
        self.root.after(1000, self._update_performance_display)  # Update every second

    def setup_menu(self):
        """Setup the application menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)

        # Simple rotation tools
        tools_menu.add_command(label="🔄 Show Rotation Diagnostics (Ctrl+R)",
                             command=self.print_rotation_diagnostics)

        # Other tools
        tools_menu.add_separator()
        tools_menu.add_command(label="⌨️ Configure Keybinds",
                             command=self.configure_keybinds)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)

        # Removed old rotation modes help - now use simple rotate_left/rotate_right values
        help_menu.add_command(label="⌨️ Keyboard Shortcuts",
                            command=self.show_keyboard_help)
        help_menu.add_command(label="ℹ️ About",
                            command=self.show_about)

    def setup_ui(self):
        """Setup the main control interface"""
        # Create menu bar
        self.setup_menu()

        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = ttk.Label(main_frame, text="🎭 Theater Projection System", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 15))
        
        # File selection frame
        file_frame = ttk.LabelFrame(main_frame, text="Media Library", padding=10)
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        # File selection buttons - responsive layout
        btn_frame = ttk.Frame(file_frame)
        btn_frame.pack(fill=tk.X)

        # Left side buttons
        left_btn_frame = ttk.Frame(btn_frame)
        left_btn_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.add_files_btn = ttk.Button(left_btn_frame, text="📁 Add Media Files",
                  command=self.add_media_files)
        self.add_files_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.edit_controls.append(self.add_files_btn)

        self.add_folder_btn = ttk.Button(left_btn_frame, text="📂 Add Folder",
                  command=self.add_media_folder)
        self.add_folder_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.edit_controls.append(self.add_folder_btn)

        self.add_blackout_btn = ttk.Button(left_btn_frame, text="⚫ Add Blackout",
                  command=self.add_blackout_cue)
        self.add_blackout_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.edit_controls.append(self.add_blackout_btn)

        # Right side button
        self.clear_all_btn = ttk.Button(btn_frame, text="🗑️ Clear All",
                  command=self.clear_cue_list)
        self.clear_all_btn.pack(side=tk.RIGHT)
        self.edit_controls.append(self.clear_all_btn)

        # Quality selection frame - responsive layout
        quality_frame = ttk.Frame(file_frame)
        quality_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(quality_frame, text="🎬 Quality Mode:").pack(side=tk.LEFT, padx=(0, 5))

        self.quality_var = tk.StringVar(value=self.quality_mode)
        self.quality_combo = ttk.Combobox(quality_frame, textvariable=self.quality_var,
                                         values=["4K", "1080p"], state="readonly", width=8)
        self.quality_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.quality_combo.bind('<<ComboboxSelected>>', self.on_quality_change)

        # Quality info label - wraps on small windows
        self.quality_info_label = ttk.Label(quality_frame, text="(3840×2160 - High Quality)",
                                           foreground="gray")
        self.quality_info_label.pack(side=tk.LEFT, padx=(0, 10))
        
        # Cue list frame
        cue_frame = ttk.LabelFrame(main_frame, text="Cue List", padding=10)
        cue_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Create treeview for table-like display
        tree_frame = ttk.Frame(cue_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        # Define columns (notes first, then rotation like fade system)
        columns = ('cue', 'notes', 'function', 'fade_in', 'fade_out', 'rotate_left', 'rotate_right', 'fill')
        self.cue_treeview = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15, selectmode='extended')

        # Configure column headings and widths (notes first)
        self.cue_treeview.heading('cue', text='Cue')
        self.cue_treeview.heading('notes', text='Notes')
        self.cue_treeview.heading('function', text='Function')
        self.cue_treeview.heading('fade_in', text='Fade In')
        self.cue_treeview.heading('fade_out', text='Fade Out')
        self.cue_treeview.heading('rotate_left', text='Rotate Left')
        self.cue_treeview.heading('rotate_right', text='Rotate Right')
        self.cue_treeview.heading('fill', text='Fill Screen')

        # Responsive column sizing - adjusts to window size (notes first)
        self.cue_treeview.column('cue', width=60, minwidth=40)
        self.cue_treeview.column('notes', width=150, minwidth=80, stretch=True)
        self.cue_treeview.column('function', width=250, minwidth=150, stretch=True)
        self.cue_treeview.column('fade_in', width=70, minwidth=60)
        self.cue_treeview.column('fade_out', width=70, minwidth=60)
        self.cue_treeview.column('rotate_left', width=80, minwidth=60)
        self.cue_treeview.column('rotate_right', width=80, minwidth=60)
        self.cue_treeview.column('fill', width=70, minwidth=50, anchor=tk.CENTER)
        
        # Configure treeview font
        style = ttk.Style()
        style.configure("Treeview", font=('Arial', 9))
        style.configure("Treeview.Heading", font=('Arial', 9, 'bold'))
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.cue_treeview.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.cue_treeview.xview)
        self.cue_treeview.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.cue_treeview.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Add treeview to edit controls for disabling during playback
        self.edit_controls.append(self.cue_treeview)
        
        # Edit Controls Frame - More prominent placement
        edit_frame = ttk.LabelFrame(main_frame, text="📝 Edit Controls", padding=10)
        edit_frame.pack(fill=tk.X, pady=(0, 10))

        # Primary edit actions (top row) - responsive layout
        edit_row1 = ttk.Frame(edit_frame)
        edit_row1.pack(fill=tk.X, pady=(0, 8))

        # Left side edit buttons
        edit_left = ttk.Frame(edit_row1)
        edit_left.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.remove_btn = ttk.Button(edit_left, text="🗑️ Remove",
                                    command=self.remove_selected_cue)
        self.remove_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.edit_controls.append(self.remove_btn)

        self.copy_btn = ttk.Button(edit_left, text="📋 Copy",
                                  command=self.copy_cue)
        self.copy_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.edit_controls.append(self.copy_btn)

        self.paste_btn = ttk.Button(edit_left, text="📄 Paste",
                                   command=self.paste_cue)
        self.paste_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.edit_controls.append(self.paste_btn)

        # Right side undo/redo buttons
        edit_right = ttk.Frame(edit_row1)
        edit_right.pack(side=tk.RIGHT)

        self.undo_btn = ttk.Button(edit_right, text="↶ Undo",
                                  command=self.undo_action)
        self.undo_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.edit_controls.append(self.undo_btn)

        self.redo_btn = ttk.Button(edit_right, text="↷ Redo",
                                  command=self.redo_action)
        self.redo_btn.pack(side=tk.LEFT)
        self.edit_controls.append(self.redo_btn)

        # Movement controls (second row) - responsive layout
        move_row = ttk.Frame(edit_frame)
        move_row.pack(fill=tk.X)

        # Left side - move controls
        move_left = ttk.Frame(move_row)
        move_left.pack(side=tk.LEFT)

        ttk.Label(move_left, text="🔄 Move:", font=('Arial', 9, 'bold')).pack(side=tk.LEFT, padx=(0, 5))

        self.move_up_btn = ttk.Button(move_left, text="⬆️ Up",
                  command=self.move_cue_up)
        self.move_up_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.edit_controls.append(self.move_up_btn)

        self.move_down_btn = ttk.Button(move_left, text="⬇️ Down",
                  command=self.move_cue_down)
        self.move_down_btn.pack(side=tk.LEFT, padx=(0, 10))
        self.edit_controls.append(self.move_down_btn)

        # Right side - helpful text (hides on small windows)
        move_tip = ttk.Label(move_row, text="💡 Tip: Select multiple cues to move together",
                 font=('Arial', 8), foreground='gray')
        move_tip.pack(side=tk.RIGHT)
        
        # Projection controls frame
        proj_frame = ttk.LabelFrame(main_frame, text="Playback Controls", padding=10)
        proj_frame.pack(fill=tk.X)
        
        # Responsive playback controls - organized in rows

        # Row 1: Navigation and playback controls
        nav_row1 = ttk.Frame(proj_frame)
        nav_row1.pack(fill=tk.X, pady=(0, 5))

        # Navigation controls
        nav_left = ttk.Frame(nav_row1)
        nav_left.pack(side=tk.LEFT)

        self.up_cue_btn = ttk.Button(nav_left, text="⬆️ Up Cue",
                  command=self.go_to_up_cue, state='disabled')
        self.up_cue_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.playback_controls.append(self.up_cue_btn)

        self.down_cue_btn = ttk.Button(nav_left, text="⬇️ Down Cue",
                  command=self.go_to_down_cue, state='disabled')
        self.down_cue_btn.pack(side=tk.LEFT, padx=(0, 10))
        self.playback_controls.append(self.down_cue_btn)

        # Playback controls
        playback_frame = ttk.Frame(nav_row1)
        playback_frame.pack(side=tk.LEFT, padx=(10, 0))

        self.play_btn = ttk.Button(playback_frame, text="▶️ Play",
                  command=self.play_action)
        self.play_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.edit_controls.append(self.play_btn)

        ttk.Button(playback_frame, text="⏹️ Stop",
                  command=self.stop_current_media).pack(side=tk.LEFT)

        # Row 2: Projection and display controls
        nav_row2 = ttk.Frame(proj_frame)
        nav_row2.pack(fill=tk.X, pady=(0, 5))

        # Projection controls
        proj_left = ttk.Frame(nav_row2)
        proj_left.pack(side=tk.LEFT)

        ttk.Button(proj_left, text="🖥️ Open Projection",
                  command=self.open_projection_window).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(proj_left, text="⚫ Black Out",
                  command=self.blackout_projection).pack(side=tk.LEFT, padx=(0, 5))
        self.fullscreen_btn = ttk.Button(proj_left, text="🔄 Fullscreen",
                  command=self.toggle_fullscreen)
        self.fullscreen_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Display controls
        display_frame = ttk.Frame(nav_row2)
        display_frame.pack(side=tk.LEFT, padx=(10, 0))

        ttk.Button(display_frame, text="🖥️ Display Info",
                  command=self.show_display_info).pack(side=tk.LEFT)

        # Rotation is now controlled via rotate_left/rotate_right columns in cue list
        # No separate rotation settings dialog needed

        # Row 3: Keyboard controls
        nav_row3 = ttk.Frame(proj_frame)
        nav_row3.pack(fill=tk.X)

        # Only show config button - keybinds are always enabled
        ttk.Button(nav_row3, text="🔧 Configure Keys",
                  command=self.configure_keybinds).pack(side=tk.LEFT)
        
        # Status bar with performance info
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=(10, 0))

        self.status_var = tk.StringVar()
        self.status_var.set("Ready - Add media files to begin")
        status_bar = ttk.Label(status_frame, textvariable=self.status_var,
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Performance indicator
        self.perf_var = tk.StringVar()
        self.perf_var.set("⚪ READY")
        perf_label = ttk.Label(status_frame, textvariable=self.perf_var,
                              relief=tk.SUNKEN, anchor=tk.E, font=('Courier', 8))
        perf_label.pack(side=tk.RIGHT, padx=(5, 0))

        # Update performance display periodically
        self._update_performance_display()
        
        # Bind events
        self.cue_treeview.bind('<<TreeviewSelect>>', self.on_cue_select)
        self.cue_treeview.bind('<Double-Button-1>', self.on_double_click)
        self.cue_treeview.bind('<Button-1>', self.on_tree_click)


        
        # No hardcoded keyboard shortcuts - will be configurable
        self.root.focus_set()
        
    def truncate_filename(self, filename):
        """Truncate filename if it exceeds max length"""
        if len(filename) <= self.max_filename_length:
            return filename
        else:
            # Keep extension and truncate the name part
            name, ext = os.path.splitext(filename)
            available_length = self.max_filename_length - len(ext) - 3  # 3 for "..."
            if available_length > 0:
                return name[:available_length] + "..." + ext
            else:
                return filename[:self.max_filename_length]
        
    def create_cue_item(self, file_path, cue_name, cue_type, fade_in=0.0, fade_out=0.0, fill=False, notes="",
                       rotate_left=0.0, rotate_right=0.0):
        """Create a cue item with simple rotation like fade system"""
        return {
            'path': file_path,
            'name': cue_name,
            'type': cue_type,
            'fade_in': fade_in,
            'fade_out': fade_out,
            'fill': fill,
            'notes': notes,
            'rotate_left': rotate_left,
            'rotate_right': rotate_right
        }
        
    def format_cue_display(self, index, cue):
        """Format cue for display in listbox - no longer used"""
        truncated_name = self.truncate_filename(cue['name'])
        fade_info = f"FI:{cue['fade_in']:.1f}s FO:{cue['fade_out']:.1f}s"
        return f"{index+1:02d}. {cue['type']} - {truncated_name} [{fade_info}]"
        
    def add_media_files(self):
        """Add individual media files to the cue list with async processing"""
        all_formats = []
        for formats in self.supported_formats.values():
            all_formats.extend(formats)

        filetypes = [
            ('All Supported', ' '.join(f'*{fmt}' for fmt in all_formats)),
            ('Images', ' '.join(f'*{fmt}' for fmt in self.supported_formats['images'])),
            ('Videos', ' '.join(f'*{fmt}' for fmt in self.supported_formats['videos'])),
            ('All Files', '*.*')
        ]

        files = filedialog.askopenfilenames(
            title="Select Media Files",
            filetypes=filetypes
        )

        if files:
            # Process files asynchronously to prevent UI blocking
            self.status_var.set(f"📁 Loading {len(files)} files...")
            self.preload_executor.submit(self._add_files_async, files)

    def _add_files_async(self, files):
        """Asynchronously add files to prevent UI blocking"""
        try:
            self._batch_adding = False  # Reset batch flag
            files_added = 0

            for file in files:
                # Update progress on UI thread
                self.root.after(0, lambda f=files_added, t=len(files):
                    self.status_var.set(f"📁 Loading files... {f+1}/{t}"))

                # Add file on UI thread to maintain thread safety
                self.root.after(0, self.add_file_to_cue_list, file)
                files_added += 1

                # Small delay to prevent overwhelming the UI
                time.sleep(0.01)

            # Clear batch flag after adding all files
            self.root.after(0, self._finish_batch_add, files_added)

        except Exception as e:
            self.root.after(0, lambda: self.status_var.set(f"❌ Error loading files: {str(e)}"))

    def _finish_batch_add(self, count):
        """Finish batch adding process"""
        if hasattr(self, '_batch_adding'):
            delattr(self, '_batch_adding')
        self.status_var.set(f"✅ Added {count} files successfully")

        # Ensure all cues have proper rotation fields
        self.migrate_cue_rotation_data()

        self.start_preloading()  # Start preloading after batch add

    def add_media_folder(self):
        """Add all media files from a selected folder with async processing"""
        folder = filedialog.askdirectory(title="Select Media Folder")
        if folder:
            self.status_var.set("📂 Scanning folder...")
            self.preload_executor.submit(self._add_folder_async, folder)

    def _add_folder_async(self, folder):
        """Asynchronously scan and add folder contents"""
        try:
            all_formats = []
            for formats in self.supported_formats.values():
                all_formats.extend(formats)

            # Scan folder for media files
            media_files = []
            for file_path in Path(folder).rglob('*'):
                if file_path.suffix.lower() in all_formats:
                    media_files.append(str(file_path))

            if media_files:
                # Update UI with count
                self.root.after(0, lambda: self.status_var.set(f"📂 Found {len(media_files)} files, adding..."))

                # Add files using existing async method
                self._add_files_async(media_files)
            else:
                self.root.after(0, lambda: self.status_var.set("📂 No media files found in folder"))

        except Exception as e:
            self.root.after(0, lambda: self.status_var.set(f"❌ Error scanning folder: {str(e)}"))
                    
    def add_file_to_cue_list(self, file_path):
        """Add a single file to the cue list (allows duplicates)"""
        # Save state for undo only for the first file added in a batch
        if len(self.cue_list) == 0 or not hasattr(self, '_batch_adding'):
            self.save_state_for_undo()
            self._batch_adding = True
            
        file_name = os.path.basename(file_path)
        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext in self.supported_formats['images']:
            media_type = "🖼️ Image"
            # Images default to fit to screen (fill=False)
            cue_item = self.create_cue_item(file_path, file_name, media_type, fill=False)
        elif file_ext in self.supported_formats['videos']:
            media_type = "🎬 Video"
            # Videos default to fit to screen (fill=False)
            cue_item = self.create_cue_item(file_path, file_name, media_type, fill=False)
        else:
            media_type = "📄 File"
            cue_item = self.create_cue_item(file_path, file_name, media_type)
        
        # Insert underneath selected cue (or at end if nothing selected)
        selection = self.get_selected_indices()
        if selection:
            # Find the furthest down selected item and insert after it
            insert_position = max(selection) + 1
        else:
            # No selection, add at the end
            insert_position = len(self.cue_list)
            
        # Insert at the calculated position
        self.cue_list.insert(insert_position, cue_item)
        
        # Refresh the entire list to update numbering
        self.refresh_cue_list()
        
        # Select the newly added item
        self.select_cue_by_index(insert_position)
        
        fade_info = f"FI:{cue_item['fade_in']:.1f}s FO:{cue_item['fade_out']:.1f}s"
        self.status_var.set(f"✅ Added: {file_name} at position {insert_position + 1} [{fade_info}] ({len(self.cue_list)} total items)")
            
    def add_blackout_cue(self):
        """Add a blackout cue to the cue list"""
        self.save_state_for_undo()
        
        cue_item = self.create_cue_item('BLACKOUT', 'Blackout', '⚫ Blackout')
        
        # Insert underneath selected cue (or at end if nothing selected)
        selection = self.get_selected_indices()
        if selection:
            # Find the furthest down selected item and insert after it
            insert_position = max(selection) + 1
        else:
            # No selection, add at the end
            insert_position = len(self.cue_list)
            
        # Insert at the calculated position
        self.cue_list.insert(insert_position, cue_item)
        
        # Refresh the entire list to update numbering
        self.refresh_cue_list()
        
        # Select the newly added blackout
        self.select_cue_by_index(insert_position)
        
        fade_info = f"FI:{cue_item['fade_in']:.1f}s FO:{cue_item['fade_out']:.1f}s"
        self.status_var.set(f"✅ Added blackout cue at position {insert_position + 1} [{fade_info}] ({len(self.cue_list)} total items)")

        # Start preloading after adding cues
        self.start_preloading()

    def start_preloading(self):
        """Start background preloading of upcoming cues"""
        if not self.preload_active and self.cue_list:
            self.preload_active = True
            self.preload_executor.submit(self._preload_worker)

    def _preload_worker(self):
        """Background worker for preloading media with intelligent prioritization"""
        try:
            # Preload images around current position with priority
            start_index = max(0, self.current_cue_index)
            end_index = min(len(self.cue_list), start_index + self.preload_distance + 1)

            # Priority 1: Next immediate cue (most important)
            if start_index + 1 < len(self.cue_list):
                next_cue = self.cue_list[start_index + 1]
                if next_cue['path'] != 'BLACKOUT':
                    file_ext = os.path.splitext(next_cue['path'])[1].lower()
                    if file_ext in self.supported_formats['images']:
                        self._preload_image(next_cue['path'])

            # Priority 2: Current cue (if not already cached)
            if start_index < len(self.cue_list):
                current_cue = self.cue_list[start_index]
                if current_cue['path'] != 'BLACKOUT':
                    file_ext = os.path.splitext(current_cue['path'])[1].lower()
                    if file_ext in self.supported_formats['images']:
                        self._preload_image(current_cue['path'])

            # Priority 3: Remaining cues in range
            for i in range(start_index + 2, end_index):
                if not self.preload_active:
                    break

                cue = self.cue_list[i]
                if cue['path'] != 'BLACKOUT':
                    file_ext = os.path.splitext(cue['path'])[1].lower()
                    if file_ext in self.supported_formats['images']:
                        self._preload_image(cue['path'])

        except Exception as e:
            print(f"Preload worker error: {e}")
        finally:
            self.preload_active = False

    def _preload_image(self, image_path):
        """Preload and cache an image at current screen size"""
        try:
            # Get current projection window size for optimal caching
            if self.projection_window and self.projection_window.winfo_exists():
                self.projection_window.update_idletasks()
                window_width = self.projection_window.winfo_width()
                window_height = self.projection_window.winfo_height()
            else:
                # Use current projection size or default
                window_width, window_height = self.projection_size

            # Load original image
            image = Image.open(image_path)

            # Preload both fill and fit versions for instant access
            cache_key_fit = (image_path, False, self.quality_mode)
            cache_key_fill = (image_path, True, self.quality_mode)

            # Check if already cached at current size
            if cache_key_fit not in self.media_cache:
                processed_image_fit = self._process_image_for_display(image, window_width, window_height, False)
                self._add_to_cache(cache_key_fit, processed_image_fit)
                print(f"🔍 DEBUG: Preloaded image (fit): {os.path.basename(image_path)} at {window_width}x{window_height}")

            if cache_key_fill not in self.media_cache:
                processed_image_fill = self._process_image_for_display(image, window_width, window_height, True)
                self._add_to_cache(cache_key_fill, processed_image_fill)
                print(f"🔍 DEBUG: Preloaded image (fill): {os.path.basename(image_path)} at {window_width}x{window_height}")

        except Exception as e:
            print(f"Failed to preload image {image_path}: {e}")

    def _process_image_for_display(self, image, window_width, window_height, fill_screen=False):
        """Process image for display. If fill_screen is True, stretch to fill window (may distort),
        otherwise preserve aspect ratio with letterboxing."""

        # Apply quality-based resolution limits
        quality_settings = self.quality_settings[self.quality_mode]
        max_width = quality_settings["max_width"]
        max_height = quality_settings["max_height"]

        # Limit the target resolution based on quality mode
        if not fill_screen:
            # For fit mode, respect quality limits while maintaining aspect ratio
            if window_width > max_width or window_height > max_height:
                scale_factor = min(max_width / window_width, max_height / window_height)
                window_width = int(window_width * scale_factor)
                window_height = int(window_height * scale_factor)
        else:
            # For fill mode, directly limit to quality settings
            window_width = min(window_width, max_width)
            window_height = min(window_height, max_height)

        # Calculate scaling to fit window while maintaining aspect ratio
        if fill_screen:
            new_width, new_height = window_width, window_height
        else:
            image_ratio = image.width / image.height
            window_ratio = window_width / window_height

            if image_ratio > window_ratio:
                # Image is wider than window ratio
                new_width = window_width
                new_height = int(window_width / image_ratio)
            else:
                # Image is taller than window ratio
                new_height = window_height
                new_width = int(window_height * image_ratio)

        # Ensure minimum size
        new_width = max(1, new_width)
        new_height = max(1, new_height)

        # Performance optimization: Skip resize if already correct size
        if image.width == new_width and image.height == new_height:
            return ImageTk.PhotoImage(image)

        # Choose resampling method based on quality mode and scale factor
        scale_factor = (new_width * new_height) / (image.width * image.height)

        # Get quality-specific resampling method
        quality_resampling = quality_settings["resampling"]

        if quality_resampling == "LANCZOS":
            if scale_factor > 1.0:
                # Upscaling - use LANCZOS for quality
                resampling = Image.Resampling.LANCZOS
            elif scale_factor > 0.5:
                # Moderate downscaling - use LANCZOS
                resampling = Image.Resampling.LANCZOS
            else:
                # Heavy downscaling - use NEAREST for speed, then LANCZOS
                # First pass: quick downscale to 2x target size
                intermediate_width = new_width * 2
                intermediate_height = new_height * 2
                if intermediate_width < image.width and intermediate_height < image.height:
                    image = image.resize((intermediate_width, intermediate_height), Image.Resampling.NEAREST)
                resampling = Image.Resampling.LANCZOS
        else:  # BICUBIC for 1080p mode
            if scale_factor > 1.0:
                # Upscaling - use BICUBIC for good quality/speed balance
                resampling = Image.Resampling.BICUBIC
            elif scale_factor > 0.25:
                # Moderate downscaling - use BICUBIC
                resampling = Image.Resampling.BICUBIC
            else:
                # Heavy downscaling - use NEAREST for speed, then BICUBIC
                intermediate_width = new_width * 2
                intermediate_height = new_height * 2
                if intermediate_width < image.width and intermediate_height < image.height:
                    image = image.resize((intermediate_width, intermediate_height), Image.Resampling.NEAREST)
                resampling = Image.Resampling.BICUBIC

        resized_image = image.resize((new_width, new_height), resampling)
        return ImageTk.PhotoImage(resized_image)

    def _add_to_cache(self, key, value):
        """Add item to cache with size limit management"""
        # Remove oldest items if cache is full
        if len(self.media_cache) >= self.cache_size_limit:
            # Remove oldest 20% of items, but at least 1 item
            items_to_remove = max(1, len(self.media_cache) // 5)
            for _ in range(items_to_remove):
                if self.media_cache:
                    oldest_key = next(iter(self.media_cache))
                    del self.media_cache[oldest_key]

        self.media_cache[key] = value

    def get_cached_image(self, cache_key):
        """Get cached image if available"""
        return self.media_cache.get(cache_key)

    def clear_media_cache(self):
        """Clear the media cache"""
        self.media_cache.clear()
        print("🔍 DEBUG: Media cache cleared")

    def on_quality_change(self, event=None):
        """Handle quality mode change"""
        new_quality = self.quality_var.get()
        if new_quality != self.quality_mode:
            self.quality_mode = new_quality

            # Update info label
            if new_quality == "4K":
                self.quality_info_label.config(text="(3840×2160 - High Quality)")
            else:  # 1080p
                self.quality_info_label.config(text="(1920×1080 - Standard Quality)")

            # Update rotation engine FPS settings for new quality mode
            if self.rotation_engine:
                self.rotation_engine.update_quality_fps_settings(new_quality)

            # Clear cache to force re-processing with new quality settings
            self.clear_media_cache()

            # Update status with FPS info
            fps_info = ""
            if self.rotation_engine:
                fps_info = f" (Rotation: {self.rotation_engine.min_fps}-{self.rotation_engine.max_fps} FPS)"
            self.status_var.set(f"✅ Quality mode changed to {new_quality}{fps_info}")
            print(f"🎬 Quality mode changed to: {new_quality}{fps_info}")

    def on_window_resize(self, event=None):
        """Handle main window resize for responsive behavior"""
        # Only handle resize events for the main window, not child widgets
        if event and event.widget != self.root:
            return

        try:
            # Get current window size
            window_width = self.root.winfo_width()
            window_height = self.root.winfo_height()

            # Adjust UI elements based on window size
            if window_width < 1000:
                # Compact mode for smaller windows
                self._set_compact_mode(True)
            else:
                # Normal mode for larger windows
                self._set_compact_mode(False)

            # Ensure minimum height for playback controls visibility
            if window_height < 650:
                # If window is too short, reduce treeview height to ensure controls are visible
                if hasattr(self, 'cue_treeview'):
                    self.cue_treeview.configure(height=8)

        except Exception as e:
            # Ignore resize errors during window creation
            pass

    def _set_compact_mode(self, compact=True):
        """Adjust UI for compact or normal mode"""
        try:
            if compact:
                # Reduce treeview height for smaller windows, but ensure playback controls remain visible
                if hasattr(self, 'cue_treeview'):
                    self.cue_treeview.configure(height=8)  # Reduced further to ensure controls are visible

                # Hide tip labels on small windows
                for widget in self.root.winfo_children():
                    self._hide_tips_recursive(widget)
            else:
                # Normal treeview height
                if hasattr(self, 'cue_treeview'):
                    self.cue_treeview.configure(height=12)  # Slightly reduced from 15 to ensure controls fit

                # Show tip labels on larger windows
                for widget in self.root.winfo_children():
                    self._show_tips_recursive(widget)

        except Exception:
            pass

    def _hide_tips_recursive(self, widget):
        """Recursively hide tip labels"""
        try:
            # Hide widgets with tip text
            if hasattr(widget, 'cget'):
                text = widget.cget('text') if 'text' in widget.configure() else ''
                if text.startswith('💡'):
                    widget.pack_forget()

            # Recurse through children
            for child in widget.winfo_children():
                self._hide_tips_recursive(child)
        except Exception:
            pass

    def _show_tips_recursive(self, widget):
        """Recursively show tip labels"""
        try:
            # Show widgets with tip text
            if hasattr(widget, 'cget'):
                text = widget.cget('text') if 'text' in widget.configure() else ''
                if text.startswith('💡'):
                    widget.pack(side=tk.RIGHT)

            # Recurse through children
            for child in widget.winfo_children():
                self._show_tips_recursive(child)
        except Exception:
            pass

    def select_cue_by_index(self, index):
        """Select a cue by its index (clears existing selection)"""
        if 0 <= index < len(self.cue_list):
            # Clear current selection
            self.cue_treeview.selection_remove(self.cue_treeview.selection())
            # Get the item at the specified index
            children = self.cue_treeview.get_children()
            if index < len(children):
                self.cue_treeview.selection_set(children[index])
                self.cue_treeview.focus(children[index])

    def add_cue_to_selection(self, index):
        """Add a cue to the current selection (doesn't clear existing selection)"""
        if 0 <= index < len(self.cue_list):
            children = self.cue_treeview.get_children()
            if index < len(children):
                self.cue_treeview.selection_add(children[index])
    
        
    def save_state_for_undo(self):
        """Save current state for undo functionality"""
        # Remove any states after current position (for redo chain breaking)
        self.cue_history = self.cue_history[:self.history_index + 1]
        
        # Add current state
        current_state = [cue.copy() for cue in self.cue_list]
        self.cue_history.append(current_state)
        self.history_index += 1
        
        # Limit history size
        if len(self.cue_history) > 50:
            self.cue_history.pop(0)
            self.history_index -= 1
            
    def copy_cue(self):
        """Copy selected cue(s) to clipboard"""
        selection = self.get_selected_indices()
        if selection:
            if len(selection) == 1:
                # Single item
                idx = selection[0]
                self.clipboard = [self.cue_list[idx].copy()]
                self.status_var.set(f"Copied: {self.clipboard[0]['name']}")
            else:
                # Multiple items
                self.clipboard = [self.cue_list[idx].copy() for idx in selection]
                self.status_var.set(f"Copied {len(self.clipboard)} cues")
        else:
            self.status_var.set("No cue selected to copy")
            
    def paste_cue(self):
        """Paste cue(s) from clipboard"""
        if self.clipboard:
            self.save_state_for_undo()
            
            # Insert after selected item using same logic as add functions
            selection = self.get_selected_indices()
            if selection:
                # Find the furthest down selected item and insert after it
                insert_index = max(selection) + 1
            else:
                # No selection, add at the end
                insert_index = len(self.cue_list)
            
            # Insert all items from clipboard
            for i, cue in enumerate(self.clipboard):
                self.cue_list.insert(insert_index + i, cue.copy())
                
            self.refresh_cue_list()
            
            # Select the pasted items
            self.cue_treeview.selection_remove(self.cue_treeview.selection())
            for i in range(len(self.clipboard)):
                self.add_cue_to_selection(insert_index + i)
            
            if len(self.clipboard) == 1:
                self.status_var.set(f"✅ Pasted: {self.clipboard[0]['name']} at position {insert_index + 1}")
            else:
                self.status_var.set(f"✅ Pasted {len(self.clipboard)} cues starting at position {insert_index + 1}")
        else:
            self.status_var.set("❌ Nothing to paste")
            
    def undo_action(self):
        """Undo last action"""
        if self.history_index > 0:
            self.history_index -= 1
            self.cue_list = [cue.copy() for cue in self.cue_history[self.history_index]]
            self.refresh_cue_list()
            self.status_var.set("Undo completed")
        else:
            self.status_var.set("Nothing to undo")
            
    def redo_action(self):
        """Redo last undone action"""
        if self.history_index < len(self.cue_history) - 1:
            self.history_index += 1
            self.cue_list = [cue.copy() for cue in self.cue_history[self.history_index]]
            self.refresh_cue_list()
            self.status_var.set("Redo completed")
        else:
            self.status_var.set("Nothing to redo")
            
    def setup_keybinds(self):
        """Setup keybinds if enabled"""
        # Clear all existing bindings first (including custom ones)
        keys_to_clear = ['<Up>', '<Down>', '<Left>', '<Right>', '<space>', '<Escape>',
                        '<Delete>', '<BackSpace>', '<Control-c>', '<Control-v>',
                        '<Control-z>', '<Control-Shift-Z>']

        # Also clear current custom keybinds
        for keybind_key in self.custom_keybinds.values():
            formatted_key = self.format_key_for_binding(keybind_key)
            keys_to_clear.append(formatted_key)

        for key in keys_to_clear:
            try:
                self.root.unbind(key)
            except:
                pass

        # Always available keybinds

        # Global fullscreen toggle for projection window - always available
        self.root.bind('<F11>', lambda e: self.toggle_fullscreen())

        # Cycle displays when in fullscreen (Shift+F11) - always available
        self.root.bind('<Shift-F11>', lambda e: self.cycle_display())

        # Only bind other keys if keybinds are enabled
        if self.keybinds_enabled:
            # Navigation keybinds (work during playback)
            up_key = self.format_key_for_binding(self.custom_keybinds['up_cue'])
            down_key = self.format_key_for_binding(self.custom_keybinds['down_cue'])
            play_key = self.format_key_for_binding(self.custom_keybinds['play'])
            stop_key = self.format_key_for_binding(self.custom_keybinds['stop'])

            print(f"🔧 DEBUG: Binding keybinds - Up: {up_key}, Down: {down_key}, Play: {play_key}, Stop: {stop_key}")

            self.root.bind(up_key, lambda e: self.go_to_up_cue())
            self.root.bind(down_key, lambda e: self.go_to_down_cue())
            self.root.bind(play_key, lambda e: self.play_action())
            self.root.bind(stop_key, lambda e: self.stop_current_media())

            # Edit keybinds (disabled during playback)
            self.setup_edit_keybinds()

    def setup_edit_keybinds(self):
        """Setup editing keybinds that get disabled during playback"""
        if self.keybinds_enabled and not self.is_playing:
            # Copy/Paste
            self.root.bind('<Control-c>', lambda e: self.copy_cue())
            self.root.bind('<Control-v>', lambda e: self.paste_cue())

            # Undo/Redo
            self.root.bind('<Control-z>', lambda e: self.undo_action())
            self.root.bind('<Control-Shift-Z>', lambda e: self.redo_action())

            # Rotation diagnostics (Ctrl+R)
            self.root.bind('<Control-r>', lambda e: self.print_rotation_diagnostics())

            # Delete key for removing cues
            self.root.bind('<Delete>', lambda e: self.remove_selected_cue())

    def clear_edit_keybinds(self):
        """Clear editing keybinds during playback"""
        for key in ['<Control-c>', '<Control-v>', '<Control-z>', '<Control-Shift-Z>',
                   '<Delete>']:
            try:
                self.root.unbind(key)
            except:
                pass

    # Keybinds are now always enabled - no toggle functionality needed

    def configure_keybinds(self):
        """Open keybind configuration dialog"""
        from tkinter import simpledialog

        config_window = tk.Toplevel(self.root)
        config_window.title("Configure Keyboard Shortcuts")
        config_window.geometry("400x300")
        config_window.transient(self.root)
        config_window.grab_set()

        # Center the window
        config_window.update_idletasks()
        x = (config_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (config_window.winfo_screenheight() // 2) - (300 // 2)
        config_window.geometry(f"400x300+{x}+{y}")

        # Create keybind configuration interface
        main_frame = ttk.Frame(config_window, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(main_frame, text="Keyboard Shortcuts Configuration",
                 font=('Arial', 12, 'bold')).pack(pady=(0, 20))

        # Info label - keybinds are always enabled
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=(0, 20))

        ttk.Label(info_frame, text="⌨️ Keyboard shortcuts are always enabled (disabled only during text editing)",
                 font=('Arial', 9), foreground='gray').pack()

        # Keybind entries
        keybind_frame = ttk.LabelFrame(main_frame, text="Key Assignments", padding=10)
        keybind_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # Instructions
        ttk.Label(keybind_frame, text="Click 'Set Key' and press the desired key:",
                 font=('Arial', 9), foreground='gray').pack(pady=(0, 10))

        self.keybind_entries = {}
        self.keybind_buttons = {}
        keybind_labels = {
            'up_cue': 'Previous Cue:',
            'down_cue': 'Next Cue:',
            'play': 'Play:',
            'stop': 'Stop:'
        }

        for i, (key, label) in enumerate(keybind_labels.items()):
            row_frame = ttk.Frame(keybind_frame)
            row_frame.pack(fill=tk.X, pady=2)

            ttk.Label(row_frame, text=label, width=15).pack(side=tk.LEFT)

            # Display current key
            key_display = ttk.Label(row_frame, text=self.custom_keybinds[key],
                                   width=15, relief=tk.SUNKEN, anchor=tk.CENTER,
                                   font=('Consolas', 9))
            key_display.pack(side=tk.LEFT, padx=(10, 5))
            self.keybind_entries[key] = key_display

            # Set key button
            set_btn = ttk.Button(row_frame, text="Set Key", width=8,
                               command=lambda k=key: self.capture_keybind(k, config_window))
            set_btn.pack(side=tk.LEFT, padx=(5, 5))
            self.keybind_buttons[key] = set_btn

            # Reset button
            reset_btn = ttk.Button(row_frame, text="Reset", width=6,
                                 command=lambda k=key: self.reset_keybind(k))
            reset_btn.pack(side=tk.LEFT)

        # Reset all button
        reset_all_frame = ttk.Frame(keybind_frame)
        reset_all_frame.pack(fill=tk.X, pady=(10, 0))
        ttk.Button(reset_all_frame, text="Reset All to Defaults",
                  command=self.reset_all_keybinds).pack()

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="Cancel",
                  command=config_window.destroy).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="Apply",
                  command=lambda: self.apply_keybind_config(config_window)).pack(side=tk.RIGHT)

    def capture_keybind(self, keybind_name, parent_window):
        """Capture a key press for keybind assignment"""
        # Create capture dialog
        capture_window = tk.Toplevel(parent_window)
        capture_window.title(f"Set Key for {keybind_name.replace('_', ' ').title()}")
        capture_window.geometry("300x150")
        capture_window.transient(parent_window)
        capture_window.grab_set()

        # Center the window
        capture_window.update_idletasks()
        x = parent_window.winfo_x() + (parent_window.winfo_width() // 2) - 150
        y = parent_window.winfo_y() + (parent_window.winfo_height() // 2) - 75
        capture_window.geometry(f"300x150+{x}+{y}")

        # Create UI
        main_frame = ttk.Frame(capture_window, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(main_frame, text=f"Setting key for: {keybind_name.replace('_', ' ').title()}",
                 font=('Arial', 10, 'bold')).pack(pady=(0, 10))

        ttk.Label(main_frame, text="Press any key...",
                 font=('Arial', 12)).pack(pady=(0, 10))

        status_label = ttk.Label(main_frame, text="Waiting for key press",
                               foreground='blue')
        status_label.pack(pady=(0, 10))

        # Cancel button
        ttk.Button(main_frame, text="Cancel",
                  command=capture_window.destroy).pack()

        # Key capture function
        def on_key_press(event):
            # Convert key to proper format
            key_name = self.format_key_name(event)

            # Update the display
            self.keybind_entries[keybind_name].configure(text=key_name)
            self.custom_keybinds[keybind_name] = key_name

            # Debug output
            print(f"🔧 DEBUG: Set {keybind_name} to '{key_name}'")

            # Close capture window
            capture_window.destroy()

        # Bind key capture
        capture_window.bind('<KeyPress>', on_key_press)
        capture_window.focus_set()

    def format_key_name(self, event):
        """Format key name for display and binding"""
        key = event.keysym

        # Handle special keys
        special_keys = {
            'Return': '<Return>',
            'space': '<space>',
            'Tab': '<Tab>',
            'BackSpace': '<BackSpace>',
            'Delete': '<Delete>',
            'Escape': '<Escape>',
            'Up': '<Up>',
            'Down': '<Down>',
            'Left': '<Left>',
            'Right': '<Right>',
            'F1': '<F1>', 'F2': '<F2>', 'F3': '<F3>', 'F4': '<F4>',
            'F5': '<F5>', 'F6': '<F6>', 'F7': '<F7>', 'F8': '<F8>',
            'F9': '<F9>', 'F10': '<F10>', 'F11': '<F11>', 'F12': '<F12>'
        }

        # Check for modifiers
        modifiers = []
        if event.state & 0x4:  # Control
            modifiers.append('Control')
        if event.state & 0x1:  # Shift
            modifiers.append('Shift')
        if event.state & 0x8:  # Alt
            modifiers.append('Alt')

        # Format the key
        if key in special_keys:
            formatted_key = special_keys[key]
        else:
            # Regular character key - always wrap in angle brackets for tkinter
            formatted_key = f"<{key.lower()}>"

        # Add modifiers
        if modifiers:
            modifier_string = '-'.join(modifiers)
            return f"<{modifier_string}-{formatted_key.strip('<>')}>"
        else:
            return formatted_key

    def format_key_for_binding(self, key_string):
        """Convert key string to proper format for tkinter binding"""
        # If it's already in angle brackets, use as-is
        if key_string.startswith('<') and key_string.endswith('>'):
            return key_string

        # If it's a single character, wrap in angle brackets
        if len(key_string) == 1:
            return f"<{key_string}>"

        # For other cases, assume it needs angle brackets
        return f"<{key_string}>"

    def reset_keybind(self, keybind_name):
        """Reset a single keybind to default"""
        defaults = {
            'up_cue': '<Up>',
            'down_cue': '<Down>',
            'play': '<space>',
            'stop': '<Escape>'
        }

        if keybind_name in defaults:
            self.custom_keybinds[keybind_name] = defaults[keybind_name]
            # Update the display
            if hasattr(self, 'keybind_entries') and keybind_name in self.keybind_entries:
                self.keybind_entries[keybind_name].configure(text=defaults[keybind_name])

    def reset_all_keybinds(self):
        """Reset all keybinds to defaults"""
        defaults = {
            'up_cue': '<Up>',
            'down_cue': '<Down>',
            'play': '<space>',
            'stop': '<Escape>'
        }

        self.custom_keybinds.update(defaults)

        # Update all displays
        if hasattr(self, 'keybind_entries'):
            for key, default_value in defaults.items():
                if key in self.keybind_entries:
                    self.keybind_entries[key].configure(text=default_value)

    def apply_keybind_config(self, window):
        """Apply keybind configuration"""
        # Keybind values are already updated by capture_keybind method
        # No need to read from entries since they're now labels

        # Apply the new configuration
        self.setup_keybinds()

        # Close window
        window.destroy()

        self.status_var.set("⌨️ Keyboard shortcuts configured")

    def show_display_info(self):
        """Show information about available displays"""
        try:
            # Get current display information
            monitors = self._get_monitor_info()

            # Create info window
            info_window = tk.Toplevel(self.root)
            info_window.title("Display Information")
            info_window.geometry("500x400")
            info_window.transient(self.root)
            info_window.grab_set()

            # Center the window
            info_window.update_idletasks()
            x = (info_window.winfo_screenwidth() // 2) - (500 // 2)
            y = (info_window.winfo_screenheight() // 2) - (400 // 2)
            info_window.geometry(f"500x400+{x}+{y}")

            # Create main frame
            main_frame = ttk.Frame(info_window, padding=20)
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Title
            ttk.Label(main_frame, text="🖥️ Display Information",
                     font=('Arial', 14, 'bold')).pack(pady=(0, 20))

            # Display list
            display_frame = ttk.LabelFrame(main_frame, text="Available Displays", padding=10)
            display_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

            # Create text widget with scrollbar
            text_frame = ttk.Frame(display_frame)
            text_frame.pack(fill=tk.BOTH, expand=True)

            text_widget = tk.Text(text_frame, wrap=tk.WORD, font=('Consolas', 10))
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # Add display information
            info_text = f"Detected {len(monitors)} display(s):\n\n"

            for i, (left, top, width, height) in enumerate(monitors):
                primary_str = " (Primary)" if i == 0 else ""
                current_str = " ← Currently selected" if i == self._current_display_index else ""

                info_text += f"Display {i+1}{primary_str}{current_str}:\n"
                info_text += f"  Resolution: {width} × {height}\n"
                info_text += f"  Position: ({left}, {top})\n"
                info_text += f"  Area: {width * height:,} pixels\n\n"

            # Add fullscreen status
            if self._custom_fullscreen:
                current_display = self._current_display_index + 1
                info_text += f"🖥️ Currently in fullscreen on Display {current_display}\n\n"
            else:
                info_text += "🖥️ Not currently in fullscreen mode\n\n"

            # Add keyboard shortcuts
            info_text += "Keyboard Shortcuts:\n"
            info_text += "  F11 - Toggle fullscreen / Cycle displays\n"
            info_text += "  Shift+F11 - Cycle displays (when in fullscreen)\n"
            info_text += "  Escape - Exit fullscreen\n\n"

            # Add tips
            info_text += "Tips:\n"
            info_text += "• Move the projection window to your desired display before pressing F11\n"
            info_text += "• Use F11 repeatedly to cycle through all available displays\n"
            info_text += "• The system will remember which display the window was on\n"

            text_widget.insert(tk.END, info_text)
            text_widget.configure(state='disabled')  # Make read-only

            # Close button
            ttk.Button(main_frame, text="Close",
                      command=info_window.destroy).pack(pady=(10, 0))

        except Exception as e:
            messagebox.showerror("Display Info Error", f"Failed to get display information:\n{str(e)}")



    def _initialize_displays(self):
        """Initialize display detection and show startup info"""
        try:
            monitors = self._get_monitor_info()
            if len(monitors) > 1:
                print(f"🖥️ Multi-display setup detected: {len(monitors)} displays available")
                print("💡 Use F11 to toggle fullscreen and cycle between displays")
            else:
                print(f"🖥️ Single display detected: {monitors[0][2]}x{monitors[0][3]}")
        except Exception as e:
            print(f"⚠️ Display detection failed: {e}")

    def enable_playback_controls(self):
        """Enable navigation controls when media is playing"""
        for control in self.playback_controls:
            control.configure(state='normal')
            
    def disable_playback_controls(self):
        """Disable navigation controls when no media is playing"""
        for control in self.playback_controls:
            control.configure(state='disabled')
        
    def disable_edit_controls(self):
        """Disable editing controls during playback"""
        for control in self.edit_controls:
            if hasattr(control, 'configure'):
                if isinstance(control, ttk.Treeview):
                    # For treeview, disable selection and hide fade columns but keep notes
                    control.configure(selectmode='none')
                    # Hide editing columns during playback but keep notes visible
                    control.configure(displaycolumns=('cue', 'notes', 'function'))
                else:
                    # For buttons, just disable them
                    control.configure(state='disabled')

        # Disable edit keybinds during playback
        self.clear_edit_keybinds()

        # Update status to show controls are locked
        current_status = self.status_var.get()
        if not current_status.startswith("🔒"):
            self.status_var.set(f"🔒 PLAYBACK MODE • Use Up/Down arrows to navigate")
            
    def enable_edit_controls(self):
        """Enable editing controls when not playing"""
        for control in self.edit_controls:
            if hasattr(control, 'configure'):
                if isinstance(control, ttk.Treeview):
                    # Re-enable treeview and show all columns (including rotation)
                    control.configure(selectmode='extended')
                    control.configure(displaycolumns=('cue', 'notes', 'function', 'fade_in', 'fade_out', 'rotate_left', 'rotate_right', 'fill'))
                else:
                    # Re-enable buttons
                    control.configure(state='normal')

        # Re-enable edit keybinds when not playing
        self.setup_edit_keybinds()

        # Remove lock status
        current_status = self.status_var.get()
        if current_status.startswith("🔒"):
            self.status_var.set("Ready - Controls unlocked")
            
    def clear_cue_list(self):
        """Clear all items from the cue list"""
        if self.cue_list and messagebox.askyesno("Clear Cue List", "Are you sure you want to clear all cues?"):
            self.save_state_for_undo()
            
            self.cue_list.clear()
            self.cue_treeview.delete(*self.cue_treeview.get_children())
            self.current_cue_index = -1
            self.stop_current_media()
            self.status_var.set("Cue list cleared")
            
    def on_tree_click(self, event):
        """Handle treeview click - simple single selection"""
        if not self.is_playing:
            item = self.cue_treeview.identify('item', event.x, event.y)
            column = self.cue_treeview.identify('column', event.x, event.y)

            if item and column == '#8':  # Fill column single-click toggle (position 8 in new layout)
                index = self.cue_treeview.index(item)
                if 0 <= index < len(self.cue_list):
                    # Toggle flag
                    self.cue_list[index]['fill'] = not self.cue_list[index].get('fill', False)
                    self.refresh_cue_list()
                    self.select_cue_by_index(index)
                    status = 'ON' if self.cue_list[index]['fill'] else 'OFF'
                    self.status_var.set(f"Fill Screen toggled {status} for {self.cue_list[index]['name']}")
                return  # prevent selection processing below for faster response

            if item:
                # Get index from the selected item
                index = self.cue_treeview.index(item)
                if 0 <= index < len(self.cue_list):
                    self.last_selected_index = index
                    selected_cue = self.cue_list[index]
                    fade_info = f"FI:{selected_cue['fade_in']:.1f}s FO:{selected_cue['fade_out']:.1f}s"
                    self.status_var.set(f"Selected: {selected_cue['name']} ({selected_cue['type']}) [{fade_info}]")



    def get_selected_indices(self):
        """Get list of selected cue indices"""
        selection = self.cue_treeview.selection()
        indices = []
        for item in selection:
            index = self.cue_treeview.index(item)
            if 0 <= index < len(self.cue_list):
                indices.append(index)
        return indices

            
    def safe_remove_cue(self):
        """Safely remove cue - only if not currently editing inline"""
        # Don't remove cue if user is currently editing inline
        if hasattr(self, 'edit_entry') and self.edit_entry:
            return  # User is editing, let normal text editing happen
        
        # Otherwise, remove the cue
        self.remove_selected_cue()

    def remove_selected_cue(self):
        """Remove the selected cue(s) from the list"""
        selection = self.get_selected_indices()
        if selection:
            self.save_state_for_undo()
            
            # Remove items in reverse order to maintain indices
            removed_items = []
            for idx in reversed(selection):
                removed_items.append(self.cue_list[idx]['name'])
                self.cue_list.pop(idx)
                
                # Adjust current index if necessary
                if self.current_cue_index >= idx:
                    self.current_cue_index -= 1
                    
            self.refresh_cue_list()
            
            if len(removed_items) == 1:
                self.status_var.set(f"Removed: {removed_items[0]}")
            else:
                self.status_var.set(f"Removed {len(removed_items)} cues")
            
            # Select next item if available
            if len(self.cue_list) > 0:
                first_removed = min(selection)
                new_selection = min(first_removed, len(self.cue_list) - 1)
                self.select_cue_by_index(new_selection)
        else:
            self.status_var.set("No cue selected to remove")
                
    def refresh_cue_list(self):
        """Refresh the display of the cue list"""
        # Clear existing items
        self.cue_treeview.delete(*self.cue_treeview.get_children())
        
        # Add all cues to treeview
        for i, cue in enumerate(self.cue_list):
            # Format values for display - use custom cue name if available
            step = cue.get('cue_name', f"{i+1:02d}")
            function = f"{cue['type']} - {self.truncate_filename(cue['name'])}"
            fade_in = f"{cue['fade_in']:.1f}s"
            fade_out = f"{cue['fade_out']:.1f}s"
            rotate_left = f"{cue.get('rotate_left', 0):.1f}"
            rotate_right = f"{cue.get('rotate_right', 0):.1f}"
            fill_symbol = '☑' if cue.get('fill') else '☐'
            notes = cue.get('notes', '')

            # Insert into treeview with notes first, then rotation columns
            item_id = self.cue_treeview.insert('', 'end', values=(
                step, notes, function, fade_in, fade_out, rotate_left, rotate_right, fill_symbol
            ))
            
            # Add tags for styling
            if i == self.current_cue_index:
                self.cue_treeview.item(item_id, tags=('current',))
                
        # Configure tag styles
        self.cue_treeview.tag_configure('current', background='#2196F3', foreground='white')
        self.cue_treeview.tag_configure('highlight', background='#505050', foreground='white')
        self.cue_treeview.tag_configure('dim', background='#2a2a2a', foreground='#666666')
            
    def move_cue_up(self):
        """Move selected cue(s) one position up in the list"""
        selection = self.get_selected_indices()

        if not selection:
            self.status_var.set("❌ No cue selected to move")
            return

        # Sort selection to process from top to bottom
        selection.sort()

        # Check if any cue is already at the top
        if selection[0] == 0:
            if len(selection) == 1:
                self.status_var.set("❌ Cue is already at the top")
            else:
                self.status_var.set("❌ Cannot move selection up - top cue is already at position 1")
            return

        # Save state for undo
        self.save_state_for_undo()

        # Extract selected cues first
        selected_cues = [self.cue_list[i] for i in selection]

        # Remove selected cues from back to front to maintain indices
        for index in reversed(selection):
            self.cue_list.pop(index)

        # Insert all cues at their new positions (one position up from the first selected)
        insert_position = selection[0] - 1

        for i, cue in enumerate(selected_cues):
            self.cue_list.insert(insert_position + i, cue)

        # Adjust current playing cue index if needed
        if self.current_cue_index in selection:
            # Current cue was moved
            old_pos = selection.index(self.current_cue_index)
            self.current_cue_index = insert_position + old_pos
        elif self.current_cue_index == insert_position:
            # Current cue was displaced by the move
            self.current_cue_index += len(selection)

        # Refresh display and maintain selection
        self.refresh_cue_list()

        # Select the moved items at their new positions
        self.cue_treeview.selection_remove(self.cue_treeview.selection())
        for i in range(len(selection)):
            self.add_cue_to_selection(insert_position + i)

        if len(selection) == 1:
            self.status_var.set(f"⬆️ Moved '{selected_cues[0]['name']}' up to position {insert_position + 1}")
        else:
            self.status_var.set(f"⬆️ Moved {len(selection)} cues up")
        
    def move_cue_down(self):
        """Move selected cue(s) one position down in the list"""
        selection = self.get_selected_indices()
        if not selection:
            self.status_var.set("❌ No cue selected to move")
            return

        # Sort selection to process from top to bottom
        selection.sort()

        # Check if any cue is already at the bottom
        if selection[-1] >= len(self.cue_list) - 1:
            if len(selection) == 1:
                self.status_var.set("❌ Cue is already at the bottom")
            else:
                self.status_var.set("❌ Cannot move selection down - bottom cue is already at last position")
            return

        # Save state for undo
        self.save_state_for_undo()

        # Extract selected cues first
        selected_cues = [self.cue_list[i] for i in selection]

        # Remove selected cues from back to front to maintain indices
        for index in reversed(selection):
            self.cue_list.pop(index)

        # Insert all cues at their new positions (one position down from the first selected)
        insert_position = selection[0] + 1
        for i, cue in enumerate(selected_cues):
            self.cue_list.insert(insert_position + i, cue)

        # Adjust current playing cue index if needed
        if self.current_cue_index in selection:
            # Current cue was moved
            old_pos = selection.index(self.current_cue_index)
            self.current_cue_index = insert_position + old_pos
        elif self.current_cue_index == insert_position:
            # Current cue was displaced by the move
            self.current_cue_index -= len(selection)

        # Refresh display and maintain selection
        self.refresh_cue_list()

        # Select the moved items at their new positions
        self.cue_treeview.selection_remove(self.cue_treeview.selection())
        for i in range(len(selection)):
            self.add_cue_to_selection(insert_position + i)

        if len(selection) == 1:
            self.status_var.set(f"⬇️ Moved '{selected_cues[0]['name']}' down to position {insert_position + 1}")
        else:
            self.status_var.set(f"⬇️ Moved {len(selection)} cues down")
        
    def highlight_current_cue(self):
        """Highlight the currently playing cue with blue background"""
        if 0 <= self.current_cue_index < len(self.cue_list):
            # Clear all highlighting first
            children = self.cue_treeview.get_children()
            for i, item in enumerate(children):
                if i == self.current_cue_index:
                    self.cue_treeview.item(item, tags=('current',))
                else:
                    self.cue_treeview.item(item, tags=())
            
    def clear_current_cue_highlight(self):
        """Clear all cue highlighting"""
        children = self.cue_treeview.get_children()
        for item in children:
            self.cue_treeview.item(item, tags=())
            
    def on_cue_select(self, event):
        """Handle cue list selection change"""
        selection = self.get_selected_indices()
        if selection:
            if len(selection) == 1:
                cue = self.cue_list[selection[0]]
                fade_info = f"FI:{cue['fade_in']:.1f}s FO:{cue['fade_out']:.1f}s"
                self.status_var.set(f"Selected: {cue['name']} ({cue['type']}) [{fade_info}]")
            else:
                self.status_var.set(f"Multi-select: {len(selection)} cues selected")
        else:
            self.status_var.set("Ready - Add media files to begin")
            
    def on_double_click(self, event):
        """Handle double-click for inline editing"""
        if not self.is_playing:  # Only allow editing when not playing
            item = self.cue_treeview.identify('item', event.x, event.y)
            column = self.cue_treeview.identify('column', event.x, event.y)
            
            # Toggle fill checkbox with single double-click
            if item and column == '#7':
                index = self.cue_treeview.index(item)
                if 0 <= index < len(self.cue_list):
                    self.cue_list[index]['fill'] = not self.cue_list[index].get('fill', False)
                    self.refresh_cue_list()
                    self.select_cue_by_index(index)
                    status = 'ON' if self.cue_list[index]['fill'] else 'OFF'
                    self.status_var.set(f"Fill Screen toggled {status} for {self.cue_list[index]['name']}")
                return "break"

            if item and column in ('#1', '#2', '#3', '#4', '#5', '#6', '#7'):  # editable columns: cue, notes, function, fade_in, fade_out, rotate_left, rotate_right
                index = self.cue_treeview.index(item)
                if 0 <= index < len(self.cue_list):
                    # Select the item
                    self.cue_treeview.selection_remove(self.cue_treeview.selection())
                    self.cue_treeview.selection_set(item)
                    
                    # Get current value and start editing (updated for new column order)
                    cue = self.cue_list[index]
                    if column == '#1':  # cue (position 1) - NEW: editable cue number/name
                        current_value = str(index + 1)  # Default to step number, but user can change
                    elif column == '#2':  # notes (position 2)
                        current_value = cue.get('notes', '')
                    elif column == '#3':  # function (position 3) - editable function name
                        current_value = cue.get('name', '')
                    elif column == '#4':  # fade_in (position 4)
                        current_value = f"{cue['fade_in']:.1f}s"
                    elif column == '#5':  # fade_out (position 5)
                        current_value = f"{cue['fade_out']:.1f}s"
                    elif column == '#6':  # rotate_left (position 6)
                        current_value = f"{cue.get('rotate_left', 0):.1f}"
                    elif column == '#7':  # rotate_right (position 7)
                        current_value = f"{cue.get('rotate_right', 0):.1f}"
                    
                    self.start_inline_edit(item, column, current_value)
                    return "break"
            elif item:
                # For other columns, just show selection info
                index = self.cue_treeview.index(item)
                if 0 <= index < len(self.cue_list):
                    self.cue_treeview.selection_remove(self.cue_treeview.selection())
                    self.cue_treeview.selection_set(item)
                    cue = self.cue_list[index]
                    fade_info = f"FI:{cue['fade_in']:.1f}s FO:{cue['fade_out']:.1f}s"
                    self.status_var.set(f"Selected: {cue['name']} ({cue['type']}) [{fade_info}] - Double-click fade/notes to edit")
                return "break"
            
    def open_projection_window(self):
        """Open the projection display window"""
        if self.projection_window is None or not self.projection_window.winfo_exists():
            # Create projection window with default size; user can toggle fullscreen (F11)
            self.projection_window = tk.Toplevel(self.root)
            self.projection_window.title("🎭 Theater Projection")
            self.projection_window.geometry("1024x768")
            self.projection_window.configure(bg='black')

            # Always on top for theater use
            self.projection_window.attributes('-topmost', True)

            # Bind F11 to toggle fullscreen
            self.projection_window.bind('<F11>', lambda e: self.toggle_fullscreen())

            # Bind Shift+F11 to cycle displays when in fullscreen
            self.projection_window.bind('<Shift-F11>', lambda e: self.cycle_display())

            # Make it always on top for theater use
            self.projection_window.attributes('-topmost', True)

            # Create main display label
            self.projection_label = tk.Label(self.projection_window, bg='black', fg='white')
            self.projection_label.pack(fill=tk.BOTH, expand=True)

            # Add text for when no media is playing
            self.projection_label.config(text="🎭 Theater Projection System\n\nReady for Media\n\nPress F11 for fullscreen",
                                       font=('Arial', 24), justify=tk.CENTER)

            # Bind escape key to exit fullscreen
            self.projection_window.bind('<Escape>', lambda e: self.exit_fullscreen())
            # Bind resize event to dynamically adjust media scaling
            self.projection_window.bind('<Configure>', self.on_projection_resize)

            # Initialise stored size
            try:
                self.projection_window.update_idletasks()
                self.projection_size = (
                    self.projection_window.winfo_width(),
                    self.projection_window.winfo_height(),
                )
            except Exception:
                pass
            self.projection_window.focus_set()

            # Initialize the ultimate rotation engine
            self.rotation_engine = UltimateRotationEngine(
                self.projection_window,
                self.projection_label,
                status_callback=lambda msg: self.status_var.set(msg)
            )
            # Set parent reference for cue access
            self.rotation_engine._parent_app = self

            # Initialize FPS settings based on current quality mode
            self.rotation_engine.update_quality_fps_settings(self.quality_mode)

            self.status_var.set("🎭 Projection window opened")
            print("🎭 Theater projection system ready")
        else:
            self.projection_window.lift()
            
    def toggle_fullscreen(self):
        """Enhanced fullscreen toggle with multi-display support and display cycling"""
        if not self.projection_window or not self.projection_window.winfo_exists():
            self.status_var.set("❌ No projection window open")
            return

        if not self._custom_fullscreen:
            # Entering fullscreen mode
            self._enter_fullscreen_mode()
        else:
            # Exiting fullscreen mode
            self._exit_fullscreen()

    def cycle_display(self):
        """Cycle through available displays when in fullscreen mode"""
        if not self._custom_fullscreen:
            self.status_var.set("💡 Enter fullscreen first to cycle displays")
            return

        if not self._available_displays or len(self._available_displays) <= 1:
            self.status_var.set("💡 Only one display available")
            return

        # Move to next display
        self._current_display_index = (self._current_display_index + 1) % len(self._available_displays)

        # Re-enter fullscreen on the new display
        self._enter_custom_fullscreen()

    def _enter_safe_fullscreen(self, target_monitor):
        """Enter fullscreen using safe/compatible methods for problematic displays"""
        try:
            left, top, width, height = target_monitor
            print(f"🖥️ Entering safe fullscreen mode: {width}x{height} at ({left}, {top})")

            # Use the most compatible approach
            self.projection_window.overrideredirect(False)
            self.projection_window.state('normal')
            self.projection_window.update_idletasks()

            # Try to use native fullscreen first on primary display
            if left == 0 and top == 0:
                try:
                    self.projection_window.attributes('-fullscreen', True)
                    self._using_native_fullscreen = True
                    print("🖥️ Using native fullscreen for primary display")
                    return True
                except:
                    pass

            # Fallback to window positioning
            self.projection_window.geometry(f"{width}x{height}+{left}+{top}")
            self.projection_window.update_idletasks()

            # Remove window decorations
            self.projection_window.overrideredirect(True)
            self.projection_window.attributes('-topmost', True)

            # Final positioning
            self.projection_window.geometry(f"{width}x{height}+{left}+{top}")

            return True

        except Exception as e:
            print(f"🖥️ Safe fullscreen failed: {e}")
            return False

    def _enter_fullscreen_mode(self):
        """Enter fullscreen mode with intelligent display selection"""
        # Store current geometry for restoration
        self.projection_window.update_idletasks()
        self._pre_fullscreen_geometry = self.projection_window.geometry()

        # Get current window position to determine which display it's on
        window_x = self.projection_window.winfo_rootx()
        window_y = self.projection_window.winfo_rooty()

        # Get available displays
        monitors = self._get_monitor_info()

        if not monitors:
            self.status_var.set("❌ No displays detected")
            return

        # Find which display the window is currently on
        current_monitor = self._find_monitor_for_window(window_x, window_y, monitors)
        if current_monitor:
            # Set current display index based on window position
            try:
                self._current_display_index = monitors.index(current_monitor)
            except ValueError:
                self._current_display_index = 0
        else:
            self._current_display_index = 0

        # Try native fullscreen first (usually works well on primary display)
        if self._current_display_index == 0 and len(monitors) == 1:
            try:
                self.projection_window.attributes('-fullscreen', True)
                self._custom_fullscreen = True
                self._using_native_fullscreen = True
                self.status_var.set("🖥️ Fullscreen enabled (native)")
                print("🖥️ Using native fullscreen")
                return
            except Exception as e:
                print(f"Native fullscreen failed: {e}")
                self._using_native_fullscreen = False

        # Use custom fullscreen for multi-display or when native fails
        self._enter_custom_fullscreen()
    
    def _enter_custom_fullscreen(self):
        """Enter custom fullscreen mode with enhanced multi-monitor support and proper positioning"""
        try:
            # Get available monitors (use cached if available)
            monitors = self._available_displays if self._available_displays else self._get_monitor_info()

            if not monitors:
                self.status_var.set("❌ No displays available")
                return

            # Use the selected display index
            if 0 <= self._current_display_index < len(monitors):
                target_monitor = monitors[self._current_display_index]
            else:
                # Fallback to first display
                target_monitor = monitors[0]
                self._current_display_index = 0

            left, top, width, height = target_monitor

            print(f"🖥️ Attempting fullscreen on display {self._current_display_index + 1}: {width}x{height} at ({left}, {top})")

            # First, ensure window is not overrideredirect to allow proper positioning
            self.projection_window.overrideredirect(False)
            self.projection_window.update_idletasks()

            # Set geometry with proper positioning - use a multi-step approach for better reliability
            geometry_string = f"{width}x{height}+{left}+{top}"
            print(f"🖥️ Setting geometry: {geometry_string}")

            # Step 1: Set the geometry
            self.projection_window.geometry(geometry_string)
            self.projection_window.update_idletasks()

            # Step 2: Force window update and positioning
            self.projection_window.update()

            # Step 3: Verify positioning and adjust if needed
            actual_x = self.projection_window.winfo_rootx()
            actual_y = self.projection_window.winfo_rooty()
            actual_width = self.projection_window.winfo_width()
            actual_height = self.projection_window.winfo_height()

            print(f"🖥️ Actual position after geometry set: {actual_width}x{actual_height} at ({actual_x}, {actual_y})")

            # If position is significantly off, try to correct it
            if abs(actual_x - left) > 50 or abs(actual_y - top) > 50:
                print(f"🖥️ Position correction needed. Expected ({left}, {top}), got ({actual_x}, {actual_y})")

                # Try multiple correction methods
                correction_methods = [
                    lambda: self.projection_window.wm_geometry(geometry_string),
                    lambda: self._try_native_positioning(left, top, width, height),
                    lambda: self._try_manual_positioning(left, top, width, height)
                ]

                for i, method in enumerate(correction_methods):
                    try:
                        method()
                        self.projection_window.update_idletasks()

                        # Check if correction worked
                        actual_x = self.projection_window.winfo_rootx()
                        actual_y = self.projection_window.winfo_rooty()
                        print(f"🖥️ Position after correction method {i+1}: ({actual_x}, {actual_y})")

                        # If position is now acceptable, break
                        if abs(actual_x - left) <= 50 and abs(actual_y - top) <= 50:
                            print(f"🖥️ Position correction successful with method {i+1}")
                            break
                    except Exception as e:
                        print(f"🖥️ Correction method {i+1} failed: {e}")
                        continue

                # If all correction methods failed, try safe mode
                final_x = self.projection_window.winfo_rootx()
                final_y = self.projection_window.winfo_rooty()
                if abs(final_x - left) > 50 or abs(final_y - top) > 50:
                    print(f"🖥️ All positioning methods failed. Trying safe fullscreen mode...")
                    if self._enter_safe_fullscreen(target_monitor):
                        print(f"🖥️ Safe fullscreen mode successful")
                        return
                    else:
                        print(f"🖥️ Safe fullscreen mode also failed. Using current position.")
                        self.status_var.set(f"⚠️ Fullscreen positioning imperfect - using best available position")

            # Step 4: Now apply fullscreen attributes
            self.projection_window.overrideredirect(True)
            self.projection_window.attributes('-topmost', True)

            # Step 5: Final geometry adjustment after overrideredirect
            self.projection_window.geometry(geometry_string)

            # Force window to front and focus
            self.projection_window.lift()
            self.projection_window.focus_force()

            self._custom_fullscreen = True
            self._using_native_fullscreen = False

            # Provide detailed status information
            display_num = self._current_display_index + 1
            total_displays = len(monitors)

            if total_displays > 1:
                self.status_var.set(f"🖥️ Fullscreen on Display {display_num}/{total_displays} ({width}x{height}) • Press F11 again to cycle displays")
                print(f"🖥️ Fullscreen enabled on display {display_num}: {width}x{height} at ({left}, {top})")
                # Update button text to show cycling capability
                self.fullscreen_btn.configure(text=f"🔄 Exit FS (Display {display_num}/{total_displays})")
            else:
                self.status_var.set(f"🖥️ Fullscreen enabled ({width}x{height})")
                print(f"🖥️ Fullscreen enabled: {width}x{height}")
                # Update button text for single display
                self.fullscreen_btn.configure(text="🔄 Exit Fullscreen")

            # Update projection size for media scaling
            self.projection_size = (width, height)

            # If there's current media, refresh it to fit the new screen size
            if hasattr(self, 'current_cue_index') and self.current_cue_index >= 0:
                self._refresh_current_media()

        except Exception as e:
            print(f"Custom fullscreen failed: {e}")
            self.status_var.set(f"❌ Fullscreen failed: {str(e)}")

    def _try_native_positioning(self, left, top, width, height):
        """Try native Windows positioning methods"""
        try:
            if platform.system() == "Windows":
                import ctypes
                from ctypes import wintypes

                # Get window handle
                hwnd = self.projection_window.winfo_id()

                # Use SetWindowPos for precise positioning
                user32 = ctypes.windll.user32
                SWP_NOZORDER = 0x0004
                SWP_NOACTIVATE = 0x0010

                # Set position and size
                user32.SetWindowPos(hwnd, 0, left, top, width, height, SWP_NOZORDER | SWP_NOACTIVATE)
                print(f"🖥️ Used SetWindowPos for positioning: {width}x{height} at ({left}, {top})")

        except Exception as e:
            print(f"🖥️ Native positioning failed: {e}")
            raise

    def _try_manual_positioning(self, left, top, width, height):
        """Try manual step-by-step positioning"""
        try:
            # Method 1: Set position first, then size
            self.projection_window.geometry(f"+{left}+{top}")
            self.projection_window.update_idletasks()

            # Then set size
            self.projection_window.geometry(f"{width}x{height}+{left}+{top}")
            self.projection_window.update_idletasks()

            print(f"🖥️ Used manual positioning: {width}x{height} at ({left}, {top})")

        except Exception as e:
            print(f"🖥️ Manual positioning failed: {e}")
            raise

    def _refresh_current_media(self):
        """Refresh current media to fit new screen size"""
        try:
            if (self.current_cue_index >= 0 and
                self.current_cue_index < len(self.cue_list) and
                self.projection_window and self.projection_window.winfo_exists()):

                current_cue = self.cue_list[self.current_cue_index]
                if current_cue['path'] != 'BLACKOUT':
                    # Re-display current media with new dimensions
                    self._display_media_immediately(current_cue)

        except Exception as e:
            print(f"Failed to refresh current media: {e}")

    def _display_media_immediately(self, cue):
        """Display media immediately without fade effects - uses preloaded images for instant display"""
        try:
            if cue['path'] == 'BLACKOUT':
                self.projection_label.configure(image='', text='')
                self.projection_label.configure(bg='black')
                return

            file_ext = os.path.splitext(cue['path'])[1].lower()

            if file_ext in self.supported_formats['images']:
                # Get current window size
                self.projection_window.update_idletasks()
                window_width = self.projection_window.winfo_width()
                window_height = self.projection_window.winfo_height()

                # Check cache first for instant display
                cache_key = (cue['path'], cue.get('fill', False), self.quality_mode)
                cached_image = self.get_cached_image(cache_key)

                if cached_image:
                    # Use preloaded image for instant display
                    photo_image = cached_image
                    print(f"🔍 DEBUG: Using cached image for instant display: {os.path.basename(cue['path'])}")
                else:
                    # Image not preloaded - process it now but this should be rare
                    print(f"⚠️ Image not preloaded, processing now: {os.path.basename(cue['path'])}")
                    image = Image.open(cue['path'])
                    photo_image = self._process_image_for_display(
                        image, window_width, window_height, cue.get('fill', False)
                    )
                    # Cache for future use
                    self._add_to_cache(cache_key, photo_image)

                # Display the image instantly
                self.projection_label.configure(image=photo_image, text='', bg='black')
                self.projection_label.image = photo_image  # Keep reference

        except Exception as e:
            print(f"Failed to display media immediately: {e}")
            self.projection_label.configure(image='', text=f'Error loading media:\n{str(e)}',
                                          fg='red', bg='black')

    def _exit_fullscreen(self):
        """Exit fullscreen mode"""
        try:
            if getattr(self, '_using_native_fullscreen', False):
                # Exit native fullscreen
                self.projection_window.attributes('-fullscreen', False)
            else:
                # Exit custom fullscreen
                self.projection_window.overrideredirect(False)
                
            # Restore original geometry
            if self._pre_fullscreen_geometry:
                self.projection_window.geometry(self._pre_fullscreen_geometry)
            else:
                # Default fallback geometry
                self.projection_window.geometry("1024x768")
                
            self._custom_fullscreen = False
            self._using_native_fullscreen = False
            self.status_var.set("🖥️ Fullscreen disabled - window restored")

            # Reset button text
            if hasattr(self, 'fullscreen_btn'):
                self.fullscreen_btn.configure(text="🔄 Fullscreen (F11)")
            
        except Exception as e:
            print(f"Exit fullscreen failed: {e}")
            self.status_var.set("❌ Exit fullscreen failed")
    
    def _get_monitor_info(self):
        """Get monitor information for the current platform with enhanced multi-display support"""
        monitors = []

        try:
            if platform.system() == "Windows":
                # Enhanced Windows monitor detection
                try:
                    import ctypes
                    from ctypes import wintypes

                    user32 = ctypes.windll.user32
                    gdi32 = ctypes.windll.gdi32

                    # Define RECT structure
                    class RECT(ctypes.Structure):
                        _fields_ = [("left", ctypes.c_long),
                                   ("top", ctypes.c_long),
                                   ("right", ctypes.c_long),
                                   ("bottom", ctypes.c_long)]

                    # Define MONITORINFO structure for getting monitor details
                    class MONITORINFO(ctypes.Structure):
                        _fields_ = [("cbSize", wintypes.DWORD),
                                   ("rcMonitor", RECT),
                                   ("rcWork", RECT),
                                   ("dwFlags", wintypes.DWORD)]

                    monitor_list = []

                    def enum_display_monitors_proc(hmonitor, hdc, rect, data):
                        # Get detailed monitor info
                        monitor_info = MONITORINFO()
                        monitor_info.cbSize = ctypes.sizeof(MONITORINFO)

                        if user32.GetMonitorInfoW(hmonitor, ctypes.byref(monitor_info)):
                            r = monitor_info.rcMonitor
                            is_primary = bool(monitor_info.dwFlags & 1)  # MONITORINFOF_PRIMARY

                            # Get DPI awareness info for better coordinate handling
                            left, top = r.left, r.top
                            width, height = r.right - r.left, r.bottom - r.top

                            # Try to get DPI scaling information
                            try:
                                # Get DPI for this monitor (Windows 8.1+)
                                shcore = ctypes.windll.shcore
                                dpi_x = ctypes.c_uint()
                                dpi_y = ctypes.c_uint()

                                # MDT_EFFECTIVE_DPI = 0
                                if shcore.GetDpiForMonitor(hmonitor, 0, ctypes.byref(dpi_x), ctypes.byref(dpi_y)) == 0:
                                    dpi_scale = dpi_x.value / 96.0  # 96 DPI is standard
                                    print(f"🖥️ Monitor DPI: {dpi_x.value} (scale: {dpi_scale:.2f})")
                                else:
                                    dpi_scale = 1.0
                            except:
                                dpi_scale = 1.0

                            monitor_data = {
                                'left': left,
                                'top': top,
                                'width': width,
                                'height': height,
                                'is_primary': is_primary,
                                'handle': hmonitor,
                                'dpi_scale': dpi_scale
                            }
                            monitor_list.append(monitor_data)

                            print(f"🖥️ Monitor detected: {width}x{height} at ({left}, {top}), DPI scale: {dpi_scale:.2f}, Primary: {is_primary}")
                        return True

                    # Define callback type
                    MONITORENUMPROC = ctypes.WINFUNCTYPE(ctypes.c_bool,
                                                       wintypes.HMONITOR, wintypes.HDC,
                                                       ctypes.POINTER(RECT), wintypes.LPARAM)

                    # Call EnumDisplayMonitors
                    user32.EnumDisplayMonitors(None, None, MONITORENUMPROC(enum_display_monitors_proc), 0)

                    # Sort monitors: primary first, then by position
                    monitor_list.sort(key=lambda m: (not m['is_primary'], m['left'], m['top']))

                    # Convert to simple tuple format for compatibility
                    monitors = [(m['left'], m['top'], m['width'], m['height']) for m in monitor_list]

                    print(f"🖥️ Detected {len(monitors)} Windows displays")
                    for i, (left, top, width, height) in enumerate(monitors):
                        primary_str = " (Primary)" if i == 0 and monitor_list[i]['is_primary'] else ""
                        print(f"   Display {i+1}: {width}x{height} at ({left}, {top}){primary_str}")

                except Exception as win_error:
                    print(f"Enhanced Windows monitor detection failed: {win_error}")
                    # Fallback for Windows
                    try:
                        width = self.root.winfo_screenwidth()
                        height = self.root.winfo_screenheight()
                        monitors.append((0, 0, width, height))
                        print(f"🖥️ Windows fallback: {width}x{height}")
                    except:
                        monitors.append((0, 0, 1920, 1080))  # Safe fallback

            elif platform.system() == "Darwin":  # macOS
                # Enhanced macOS monitor detection
                try:
                    import subprocess
                    import json

                    # Try to get display info using system_profiler
                    result = subprocess.run(['system_profiler', 'SPDisplaysDataType', '-json'],
                                          capture_output=True, text=True, timeout=5)

                    if result.returncode == 0:
                        data = json.loads(result.stdout)
                        # Parse the display data (simplified)
                        displays = data.get('SPDisplaysDataType', [])
                        for display in displays:
                            # This would need more detailed parsing for real implementation
                            pass

                    # Fallback to Tkinter screen info
                    width = self.root.winfo_screenwidth()
                    height = self.root.winfo_screenheight()
                    monitors.append((0, 0, width, height))
                    print(f"🖥️ macOS display: {width}x{height}")

                except Exception as mac_error:
                    print(f"macOS monitor detection failed: {mac_error}")
                    monitors.append((0, 0, 1920, 1080))  # Safe fallback

            elif platform.system() == "Linux":
                # Enhanced Linux monitor detection
                try:
                    import subprocess
                    import re

                    # Try xrandr first
                    result = subprocess.run(['xrandr', '--query'],
                                          capture_output=True, text=True, timeout=5)

                    if result.returncode == 0:
                        # Parse xrandr output for connected displays
                        lines = result.stdout.split('\n')
                        for line in lines:
                            if ' connected' in line and 'primary' in line:
                                # Parse primary display
                                match = re.search(r'(\d+)x(\d+)\+(\d+)\+(\d+)', line)
                                if match:
                                    width, height, x, y = map(int, match.groups())
                                    monitors.insert(0, (x, y, width, height))  # Primary first
                            elif ' connected' in line and 'primary' not in line:
                                # Parse secondary displays
                                match = re.search(r'(\d+)x(\d+)\+(\d+)\+(\d+)', line)
                                if match:
                                    width, height, x, y = map(int, match.groups())
                                    monitors.append((x, y, width, height))

                    if monitors:
                        print(f"🖥️ Detected {len(monitors)} Linux displays via xrandr")
                        for i, (x, y, width, height) in enumerate(monitors):
                            primary_str = " (Primary)" if i == 0 else ""
                            print(f"   Display {i+1}: {width}x{height} at ({x}, {y}){primary_str}")
                    else:
                        # Fallback to Tkinter
                        width = self.root.winfo_screenwidth()
                        height = self.root.winfo_screenheight()
                        monitors.append((0, 0, width, height))
                        print(f"🖥️ Linux fallback: {width}x{height}")

                except Exception as linux_error:
                    print(f"Linux monitor detection failed: {linux_error}")
                    monitors.append((0, 0, 1920, 1080))  # Safe fallback

        except Exception as e:
            print(f"Monitor detection failed: {e}")

        # Ensure we always have at least one monitor
        if not monitors:
            try:
                width = self.root.winfo_screenwidth()
                height = self.root.winfo_screenheight()
                monitors.append((0, 0, width, height))
                print(f"🖥️ Final fallback: {width}x{height}")
            except:
                monitors.append((0, 0, 1920, 1080))  # Ultimate fallback
                print("🖥️ Ultimate fallback: 1920x1080")

        # Cache the results
        self._available_displays = monitors.copy()
        return monitors
    
    def _find_monitor_for_window(self, window_x, window_y, monitors):
        """Find which monitor contains the given window coordinates"""
        for left, top, width, height in monitors:
            if (left <= window_x < left + width and 
                top <= window_y < top + height):
                return (left, top, width, height)
        
        # Return primary monitor if not found
        return monitors[0] if monitors else None
            
    def exit_fullscreen(self):
        """Exit fullscreen mode (called by Escape key)"""
        if self.projection_window and self.projection_window.winfo_exists() and self._custom_fullscreen:
            self._exit_fullscreen()
            
    def blackout_projection(self):
        """Theater blackout - instant to black"""
        print("🎭 Blackout called")
        if self.projection_window and self.projection_window.winfo_exists():
            self.projection_label.config(image='', text='', bg='black')
            
            # Only update status if not currently playing a cue
            if not self.is_playing:
                self.status_var.set("⚫ Projection blacked out")
        else:
            print("🎭 No projection window exists for blackout")
            
    # === SIMPLE FADE METHODS ===
    
    def stop_fade(self):
        """Stop any active fade operation"""
        if self.fade_system:
            self.fade_system.stop_fade()
    
    def emergency_blackout(self):
        """Emergency blackout - instant to black"""
        self.stop_fade()
        if self.projection_window and self.projection_window.winfo_exists():
            self.projection_label.config(image='', text='', bg='black')


             
    def go_to_down_cue(self):
        """Go to next cue (down in list - only works during playback)"""
        if not self.is_playing:
            self.status_var.set("⚠️ Press Play first to enable cue navigation")
            return

        if not self.cue_list:
            self.status_var.set("❌ No cues available")
            return

        if self.current_cue_index >= len(self.cue_list) - 1:
            self.status_var.set("⚠️ Already at last cue")
            return

        # Stop any existing fade
        self.stop_fade()
        
        # Get current cue fade out time
        current_cue = self.cue_list[self.current_cue_index]
        fade_out_time = current_cue.get('fade_out', 0.0)

        # Move to next cue
        next_cue_index = self.current_cue_index + 1
        next_cue = self.cue_list[next_cue_index]
        fade_in_time = next_cue.get('fade_in', 0.0)

        # CRITICAL: Preload the next cue BEFORE starting transition
        self._ensure_cue_preloaded(next_cue_index)

        # Update current index
        self.current_cue_index = next_cue_index

        try:
            # Transition to the cue with fade effects
            self._transition_to_cue(self.current_cue_index, fade_out_time, fade_in_time)

            self.status_var.set(f"🔒 PLAYBACK MODE • ⬇️ {next_cue['name']} • Use Up/Down arrows to navigate")

            # Trigger preloading of upcoming cues
            self.start_preloading()

        except Exception as e:
            self.status_var.set(f"❌ Failed to play next cue: {str(e)}")
            
    def go_to_up_cue(self):
        """Go to previous cue (up in list - only works during playback)"""
        if not self.is_playing:
            self.status_var.set("⚠️ Press Play first to enable cue navigation")
            return

        if not self.cue_list:
            self.status_var.set("❌ No cues available")
            return

        if self.current_cue_index <= 0:
            self.status_var.set("⚠️ Already at first cue")
            return

        # Stop any existing fade
        self.stop_fade()
        
        # Get current cue fade out time
        current_cue = self.cue_list[self.current_cue_index]
        fade_out_time = current_cue.get('fade_out', 0.0)

        # Move to previous cue
        previous_cue_index = self.current_cue_index - 1
        previous_cue = self.cue_list[previous_cue_index]
        fade_in_time = previous_cue.get('fade_in', 0.0)

        # CRITICAL: Preload the previous cue BEFORE starting transition
        self._ensure_cue_preloaded(previous_cue_index)

        # Update current index
        self.current_cue_index = previous_cue_index

        try:
            # Transition to the cue with fade effects
            self._transition_to_cue(self.current_cue_index, fade_out_time, fade_in_time)

            self.status_var.set(f"🔒 PLAYBACK MODE • ⬆️ {previous_cue['name']} • Use Up/Down arrows to navigate")

            # Trigger preloading of upcoming cues
            self.start_preloading()

        except Exception as e:
            self.status_var.set(f"❌ Failed to play previous cue: {str(e)}")
            
    def play_action(self):
        """Handle play button - always start playback from first cue"""
        if not self.cue_list:
            self.status_var.set("❌ No cues available - add media files first")
            messagebox.showwarning("No Content", "Please add media files or blackout cues before playing.")
            return
            
        if self.is_playing:
            self.status_var.set("⚠️ Already playing - use Stop to halt current playback")
            return
            
        # ALWAYS start from first cue when Play is pressed
        self.current_cue_index = 0
        first_cue = self.cue_list[0]
            
        # Update treeview selection
        self.select_cue_by_index(self.current_cue_index)
        
        # Ensure projection window exists
        if not self.projection_window or not self.projection_window.winfo_exists():
            self.open_projection_window()

        # CRITICAL: Preload the first cue BEFORE starting playback
        self._ensure_cue_preloaded(0)

        # Start playback
        try:
            # Handle fade in for first cue if needed
            fade_in_time = first_cue.get('fade_in', 0.0)

            # Update fade system reference
            self.fade_system.projection_window = self.projection_window
            self.fade_system.projection_label = self.projection_label
            
            if fade_in_time > 0:
                # Start with fade in
                self.fade_system.fade_in_after_switch(fade_in_time, self.play_current_cue)
            else:
                # No fade - play content directly
                self.play_current_cue()
                
            self.status_var.set(f"🔒 PLAYBACK MODE • 🎬 {first_cue['name']} • Use Up/Down arrows to navigate")
        except Exception as e:
            self.status_var.set(f"❌ Playback failed: {str(e)}")
            messagebox.showerror("Playback Error", f"Failed to play cue: {str(e)}")
            self.stop_current_media()
            
    def play_current_cue(self):
        """SIMPLIFIED: Play the current cue"""
        print(f"🔍 DEBUG: play_current_cue called")
        
        if not (0 <= self.current_cue_index < len(self.cue_list)):
            return
            
        if not self.projection_window or not self.projection_window.winfo_exists():
            print("🔍 DEBUG: Opening projection window")
            self.open_projection_window()

        cue = self.cue_list[self.current_cue_index]
        print(f"🔍 DEBUG: Playing cue: {cue['name']} (type: {cue['type']})")

        # Stop any existing media first
        was_video = self.current_video_cap is not None
        self._stop_current_media_immediately()
        
        # Mark as playing and update UI
        self.is_playing = True
        self.disable_edit_controls()
        self.enable_playback_controls()
        self.highlight_current_cue()
        
        # Handle blackout cue
        if cue['path'] == 'BLACKOUT':
            print("🔍 DEBUG: Handling blackout cue")
            self.blackout_projection()
            self.status_var.set(f"🔒 PLAYBACK MODE • {cue['name']} • Use Up/Down arrows to navigate")
            return
        
        file_ext = os.path.splitext(cue['path'])[1].lower()
        print(f"🔍 DEBUG: File extension: {file_ext}")
        
        if file_ext in self.supported_formats['images']:
            print("🔍 DEBUG: Displaying image")
            # Small delay for video-to-image transitions
            if was_video:
                self.root.after(50, lambda: self.display_image(cue['path']))
            else:
                self.display_image(cue['path'])
        elif file_ext in self.supported_formats['videos']:
            print("🔍 DEBUG: Playing video")
            self.play_video(cue['path'])
        else:
            print(f"🔍 DEBUG: Unsupported file type: {file_ext}")
            self.status_var.set(f"❌ Unsupported file type: {file_ext}")
            self.stop_current_media()
            return
            
        self.status_var.set(f"🔒 PLAYBACK MODE • {cue['name']} • Use Up/Down arrows to navigate")
    
    def _stop_current_media_immediately(self):
        """Immediately stop any current media playback"""
        # Stop video playback
        if self.current_video_cap:
            try:
                self.current_video_cap.release()
                print("🔍 DEBUG: Released video capture")
            except:
                pass
            self.current_video_cap = None
        
        # Wait for video thread to stop
        if self.video_thread and self.video_thread.is_alive():
            print("🔍 DEBUG: Waiting for video thread to stop...")
            import time
            for i in range(10):  # Max 100ms wait
                if not self.video_thread.is_alive():
                    break
                time.sleep(0.01)
            self.video_thread = None

        # Note: Don't stop rotation here - let it continue between cues
        # Only stop rotation when explicitly stopping playback

    def _transition_to_cue(self, target_cue_index, fade_out_time, fade_in_time):
        """Simple transition with fade out then fade in - ensures images are preloaded"""
        print(f"Simple transition to cue {target_cue_index}")

        # Stop any active fade first
        self.stop_fade()

        # Clear current highlighting and update selection
        self.clear_current_cue_highlight()
        self.select_cue_by_index(target_cue_index)

        # CRITICAL: Preload the target cue image BEFORE transition starts
        self._ensure_cue_preloaded(target_cue_index)

        # Update fade system reference to current projection window
        self.fade_system.projection_window = self.projection_window
        self.fade_system.projection_label = self.projection_label

        # Use fade out time if specified, otherwise fade in time
        fade_time = fade_out_time if fade_out_time > 0 else fade_in_time

        if fade_time > 0:
            if fade_out_time > 0:
                # Fade out first, then switch and fade in
                self.fade_system.fade_out_then_switch(fade_out_time, lambda: self._switch_and_fade_in(fade_in_time))
            else:
                # Just fade in
                self.fade_system.fade_in_after_switch(fade_in_time, self.play_current_cue)
        else:
            # No fade - just play the cue directly
            self.play_current_cue()

        # Update highlighting
        self.highlight_current_cue()

    def _ensure_cue_preloaded(self, cue_index):
        """Ensure the specified cue is preloaded and ready for instant display"""
        if not (0 <= cue_index < len(self.cue_list)):
            return

        cue = self.cue_list[cue_index]

        # Only preload images (videos don't need preloading)
        if cue['path'] == 'BLACKOUT':
            return  # Blackout doesn't need preloading

        file_ext = os.path.splitext(cue['path'])[1].lower()
        if file_ext not in self.supported_formats['images']:
            return  # Not an image

        try:
            # Get current projection window size
            if self.projection_window and self.projection_window.winfo_exists():
                self.projection_window.update_idletasks()
                window_width = self.projection_window.winfo_width()
                window_height = self.projection_window.winfo_height()
            else:
                window_width, window_height = self.projection_size

            # Check if image is already cached at current size
            fill_flag = cue.get('fill', False)
            cache_key = (cue['path'], fill_flag, self.quality_mode)

            if cache_key not in self.media_cache:
                print(f"🔍 DEBUG: Preloading cue {cue_index} image: {os.path.basename(cue['path'])}")

                # Load and process image immediately
                image = Image.open(cue['path'])
                processed_image = self._process_image_for_display(image, window_width, window_height, fill_flag)

                # Cache the processed image
                self._add_to_cache(cache_key, processed_image)
                print(f"🔍 DEBUG: Cue {cue_index} image preloaded and cached for instant display")
            else:
                print(f"🔍 DEBUG: Cue {cue_index} image already cached")

        except Exception as e:
            print(f"⚠️ Failed to preload cue {cue_index} image: {e}")
    
    def _switch_and_fade_in(self, fade_in_time):
        """Switch to new cue content and fade in if needed"""
        if fade_in_time > 0:
            self.fade_system.fade_in_after_switch(fade_in_time, self.play_current_cue)
        else:
            # No fade-in requested – ensure any existing black overlay is removed
            self.fade_system.stop_fade()
            self.play_current_cue()



    def display_image(self, image_path):
        """Display an image in the projection window with caching"""
        print(f"🔍 DEBUG: display_image called with path: {image_path}")
        # Note: Don't stop rotation when switching images during playback
        # Rotation should continue across cue changes
        try:
            # Check cache first for instant loading
            fill_flag = False
            # Determine fill flag from current cue (safe default False)
            if 0 <= self.current_cue_index < len(self.cue_list):
                fill_flag = self.cue_list[self.current_cue_index].get('fill', False)

            cache_key = (image_path, fill_flag, self.quality_mode)

            # Check cache first for instant loading
            cached_photo = self.get_cached_image(cache_key)
            if cached_photo:
                print("🔍 DEBUG: Using cached image - instant load!")
                self.projection_label.config(image=cached_photo, text='')
                self.projection_label.image = cached_photo  # Keep a reference

                # Initialise rotation if needed (even when using cache)
                self._init_image_rotation(image_path)

                # Trigger preloading of next images
                self.start_preloading()
                return

            # Cache miss - load and process image
            print("🔍 DEBUG: Cache miss - loading image")
            image = Image.open(image_path)
            print("🔍 DEBUG: Image opened successfully")

            # Get projection window size
            self.projection_window.update_idletasks()
            window_width = self.projection_window.winfo_width()
            window_height = self.projection_window.winfo_height()
            print(f"🔍 DEBUG: Window size: {window_width}x{window_height}")

            # Process image using optimized method
            photo = self._process_image_for_display(image, window_width, window_height, fill_flag)

            # Cache for future use
            self._add_to_cache(cache_key, photo)
            print(f"🔍 DEBUG: Image cached for future use")

            # Always set the image - if fade is active, it will be hidden by the overlay
            print("🔍 DEBUG: Setting image to projection label")
            self.projection_label.config(image=photo, text='')
            self.projection_label.image = photo  # Keep a reference

            # Initialise rotation if needed
            self._init_image_rotation(image_path)

            # Trigger preloading of next images
            self.start_preloading()

        except Exception as e:
            print(f"🔍 DEBUG: Image display failed: {str(e)}")
            self.status_var.set(f"❌ Image display failed: {str(e)}")
            messagebox.showerror("Image Error", f"Could not display image: {str(e)}")
            self.stop_current_media()
            
    def play_video(self, video_path):
        """Play a video in the projection window"""
        try:
            print(f"🔍 DEBUG: Opening video file: {video_path}")
            # Don't call stop_current_media here - is_playing is already set
            self.current_video_cap = cv2.VideoCapture(video_path)

            if not self.current_video_cap.isOpened():
                raise Exception(f"Could not open video file: {video_path}")

            print("🔍 DEBUG: Video file opened successfully")

            # CRITICAL FIX: Ensure video starts immediately by reading first frame
            ret, first_frame = self.current_video_cap.read()
            if ret:
                print("🔍 DEBUG: First video frame read successfully")
                # Reset to beginning for proper playback
                self.current_video_cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
            else:
                print("🔍 DEBUG: Warning: Could not read first video frame")

            # Initialize rotation for video if needed
            self._init_video_rotation(video_path)

            # Start video playback in a separate thread
            self.video_thread = threading.Thread(target=self._video_playback_loop, daemon=True)
            self.video_thread.start()
            print("🔍 DEBUG: Video playback thread started")

        except Exception as e:
            print(f"🔍 DEBUG: Video playback error: {e}")
            self.status_var.set(f"❌ Video playback failed: {str(e)}")
            messagebox.showerror("Video Error", f"Could not play video: {str(e)}")
            self.stop_current_media()
            
    def _update_projection_frame(self, photo):
        """Update projection frame in main thread"""
        try:
            if (self.projection_label and
                self.projection_window and
                self.projection_window.winfo_exists() and
                self.is_playing):
                # CRITICAL FIX: Always update the frame, even during fades
                # This ensures video frames replace any stuck images
                print("🔍 DEBUG: Updating video frame")
                self.projection_label.config(image=photo, text='')
                self.projection_label.image = photo

                # Force update to ensure frame is rendered
                self.projection_label.update_idletasks()
        except Exception as e:
            # Window was destroyed, stop video playback
            print(f"🔍 DEBUG: Projection window destroyed during video update: {e}")
            self.is_playing = False
            
    def _video_playback_loop(self):
        """Optimized video playback loop with frame timing and buffering"""
        if not self.current_video_cap:
            return

        # Store the video cap reference to detect if it changes
        my_video_cap = self.current_video_cap
        fps = my_video_cap.get(cv2.CAP_PROP_FPS) or self.target_fps
        frame_delay = 1.0 / fps

        # Performance tracking
        last_frame_time = time.perf_counter()
        frame_count = 0

        # Use self.projection_size which is kept updated by the main thread
        window_width, window_height = self.projection_size

        while (self.is_playing and
               my_video_cap and
               my_video_cap.isOpened() and
               my_video_cap is self.current_video_cap):

            frame_start_time = time.perf_counter()

            # ENHANCED: More frequent stop checks for responsive transitions
            if not self.is_playing or my_video_cap is not self.current_video_cap:
                print("🔍 DEBUG: Video playback stop signal detected")
                break

            # Update to latest window size for each frame (safe cross-thread read)
            window_width, window_height = self.projection_size

            ret, frame = my_video_cap.read()
            if ret:
                # Double-check we should still be running after frame read
                if not self.is_playing or my_video_cap is not self.current_video_cap:
                    print("🔍 DEBUG: Video playback stopped during frame processing")
                    break

                try:
                    # Determine fill setting for current cue once
                    fill_flag = False
                    if 0 <= self.current_cue_index < len(self.cue_list):
                        fill_flag = self.cue_list[self.current_cue_index].get('fill', False)

                    processed_photo = self._process_video_frame(frame, window_width, window_height, fill_flag)

                    # Final check before updating display
                    if (self.is_playing and
                        my_video_cap is self.current_video_cap and
                        self.projection_label and processed_photo):
                        self.root.after(0, self._update_projection_frame, processed_photo)

                except Exception as e:
                    print(f"Error processing video frame: {e}")
                    break
            else:
                # Video ended, loop it if we're still the current video
                if my_video_cap is self.current_video_cap:
                    my_video_cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                    print("🔍 DEBUG: Video looped")
                else:
                    break

            # Adaptive frame timing for smooth playback
            frame_process_time = time.perf_counter() - frame_start_time
            sleep_time = max(0, frame_delay - frame_process_time)

            if sleep_time > 0:
                time.sleep(sleep_time)

            # Performance monitoring (every 30 frames)
            frame_count += 1
            if frame_count % 30 == 0:
                current_time = time.perf_counter()
                actual_fps = 30 / (current_time - last_frame_time)
                if actual_fps < fps * 0.8:  # If FPS drops below 80% of target
                    print(f"🔍 DEBUG: Video FPS drop detected: {actual_fps:.1f}/{fps:.1f}")
                last_frame_time = current_time

    def _process_video_frame(self, frame, window_width, window_height, fill_screen=False):
        """Process a single video frame for display with rotation support"""
        try:
            # Apply quality-based resolution limits
            quality_settings = self.quality_settings[self.quality_mode]
            max_width = quality_settings["max_width"]
            max_height = quality_settings["max_height"]

            # Limit the target resolution based on quality mode
            if not fill_screen:
                # For fit mode, respect quality limits while maintaining aspect ratio
                if window_width > max_width or window_height > max_height:
                    scale_factor = min(max_width / window_width, max_height / window_height)
                    window_width = int(window_width * scale_factor)
                    window_height = int(window_height * scale_factor)
            else:
                # For fill mode, directly limit to quality settings
                window_width = min(window_width, max_width)
                window_height = min(window_height, max_height)

            # 1. BGR->RGB for PIL/Tk compatibility
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # 2. Pre-scale original frame to fit/fill the projection window – this size remains constant
            in_h, in_w = frame_rgb.shape[:2]
            if fill_screen:
                target_w, target_h = window_width, window_height
            else:
                frame_ratio = in_w / in_h
                window_ratio = window_width / window_height
                if frame_ratio > window_ratio:
                    target_w = window_width
                    target_h = int(window_width / frame_ratio)
                else:
                    target_h = window_height
                    target_w = int(window_height * frame_ratio)

            target_w = max(1, target_w)
            target_h = max(1, target_h)
            if (in_w, in_h) != (target_w, target_h):
                frame_rgb = cv2.resize(frame_rgb, (target_w, target_h), cv2.INTER_AREA)

            # 3. Apply high-performance rotation FIRST (rotate the frame in place)
            if self.rotation_engine and self.rotation_engine.is_active:
                angle = self.rotation_engine.get_current_angle()

                # Use high-performance rotation for video
                max_speed = max(self.rotation_engine.left_speed, self.rotation_engine.right_speed)
                quality = self.rotation_engine._get_optimal_quality(max_speed)

                try:
                    frame_rgb = self.rotation_engine._high_performance_rotate(frame_rgb, angle, quality)
                    # Update dimensions after rotation
                    target_h, target_w = frame_rgb.shape[:2]
                except Exception as e:
                    print(f"⚠️ High-performance video rotation failed: {e}, using fallback")
                    # Fallback to standard rotation
                    center = (target_w / 2, target_h / 2)
                    rot_mat = cv2.getRotationMatrix2D(center, angle, 1.0)
                    frame_rgb = cv2.warpAffine(frame_rgb, rot_mat, (target_w, target_h), flags=cv2.INTER_LINEAR,
                                            borderMode=cv2.BORDER_CONSTANT, borderValue=(0, 0, 0))

            # 4. Place the rotated frame onto the final canvas for display
            # Create canvas sized to the window
            canvas = np.zeros((window_height, window_width, 3), dtype=frame_rgb.dtype)

            # Center the rotated frame on the canvas
            canvas_h, canvas_w = canvas.shape[:2]
            frame_h, frame_w = frame_rgb.shape[:2]

            # Calculate position to center the frame
            y_offset = max(0, (canvas_h - frame_h) // 2)
            x_offset = max(0, (canvas_w - frame_w) // 2)

            # Calculate how much of the frame fits
            y_end = min(canvas_h, y_offset + frame_h)
            x_end = min(canvas_w, x_offset + frame_w)
            frame_y_end = min(frame_h, canvas_h - y_offset)
            frame_x_end = min(frame_w, canvas_w - x_offset)

            # Place the rotated frame centered on the canvas
            if frame_y_end > 0 and frame_x_end > 0:
                canvas[y_offset:y_end, x_offset:x_end] = frame_rgb[:frame_y_end, :frame_x_end]

            cropped = canvas

            # Convert to PhotoImage once (no further scaling)
            image = Image.fromarray(cropped)
            return ImageTk.PhotoImage(image)

        except Exception as e:
            print(f"Error processing video frame: {e}")
            return None
            
    def stop_current_media(self):
        """Stop any currently playing media with enhanced feedback"""
        was_playing = self.is_playing
        current_cue_name = ""
        
        # Get current cue info for feedback
        if was_playing and 0 <= self.current_cue_index < len(self.cue_list):
            current_cue_name = self.cue_list[self.current_cue_index]['name']
            
        # Stop any active fade effects
        self.stop_fade()
        self.stop_image_rotation()

        # Note: Unlike fade system, rotation settings are preserved when stopping
        # Users can set rotation values and they persist between play sessions

        # Stop playback immediately
        self.is_playing = False
        
        # Clean up video resources
        if self.current_video_cap:
            try:
                self.current_video_cap.release()
            except:
                pass  # Ignore cleanup errors
            self.current_video_cap = None
            
        if self.video_thread and self.video_thread.is_alive():
            self.video_thread = None
            
        # ALWAYS re-enable edit controls and disable navigation when stopping playback
        self.enable_edit_controls()
        self.disable_playback_controls()
        
        # Clear current cue highlighting
        self.clear_current_cue_highlight()
            
        # Clear projection display
        if (self.projection_window and self.projection_window.winfo_exists() and
            self.projection_label):
            self.projection_label.config(image='', text='🎭 Theater Projection System\n\n⏹️ Playback Stopped\nReady for Next Cue')
            self.projection_label.configure(bg='black', fg='white', font=('Arial', 20))
            
        # Reset to start position for next play
        self.current_cue_index = -1
            
        # Provide clear feedback
        if was_playing and current_cue_name:
            self.status_var.set(f"⏹️ Stopped: {current_cue_name} • Press Play to start from beginning")
        elif was_playing:
            self.status_var.set("⏹️ Playback stopped • Press Play to start from beginning")  
        else:
            self.status_var.set("⏹️ Already stopped • Press Play to start")
        
        return was_playing  # Return whether we actually stopped something
            
    def run(self):
        """Start the application"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except Exception as e:
            messagebox.showerror("Error", f"Application error: {str(e)}")
            
    def on_closing(self):
        """Handle application closing"""
        # Stop preloading
        self.preload_active = False
        if hasattr(self, 'preload_executor'):
            self.preload_executor.shutdown(wait=False)

        # Clear cache
        self.clear_media_cache()

        # Stop simple fade system
        if self.fade_system:
            self.fade_system.stop_fade()

        # Stop media and close windows
        self.stop_current_media()
        if self.projection_window:
            self.projection_window.destroy()
        self.root.destroy()
        
        print("Theater Projection System shutdown complete")



    def start_inline_edit(self, item, column, current_value):
        """Start inline editing of a cell"""
        # DISABLE ALL KEYBINDS DURING EDITING to prevent accidental playback
        self.disable_keybinds_for_editing()

        # Get the bounding box of the cell
        bbox = self.cue_treeview.bbox(item, column)
        if not bbox:
            return
            
        x, y, width, height = bbox
        
        # Create entry widget for editing
        self.edit_entry = tk.Entry(self.cue_treeview, font=('Arial', 9))
        self.edit_entry.place(x=x, y=y, width=width, height=height)
        self.edit_entry.insert(0, current_value)
        self.edit_entry.select_range(0, tk.END)
        self.edit_entry.focus()
        
        # Store edit context
        self.edit_item = item
        self.edit_column = column
        
        # Bind events for saving/canceling
        self.edit_entry.bind('<Return>', self.finish_inline_edit)
        self.edit_entry.bind('<Escape>', self.cancel_inline_edit)
        self.edit_entry.bind('<FocusOut>', self.finish_inline_edit)
        
    def finish_inline_edit(self, event=None):
        """Finish inline editing and save the value"""
        if hasattr(self, 'edit_entry') and self.edit_entry:
            new_value = self.edit_entry.get()
            
            # Get the item index
            index = self.cue_treeview.index(self.edit_item)
            
            if 0 <= index < len(self.cue_list):
                self.save_state_for_undo()
                
                # Update the cue data based on column (updated for new column order)
                if self.edit_column == '#1':  # cue (position 1) - editable cue number/name
                    self.cue_list[index]['cue_name'] = new_value  # Store custom cue name
                elif self.edit_column == '#2':  # notes (position 2)
                    self.cue_list[index]['notes'] = new_value
                elif self.edit_column == '#3':  # function (position 3) - editable function name
                    self.cue_list[index]['name'] = new_value
                elif self.edit_column == '#4':  # fade_in (position 4)
                    try:
                        fade_value = float(new_value.replace('s', ''))
                        self.cue_list[index]['fade_in'] = max(0.0, min(60.0, fade_value))
                    except ValueError:
                        pass  # Keep original value if invalid
                elif self.edit_column == '#5':  # fade_out (position 5)
                    try:
                        fade_value = float(new_value.replace('s', ''))
                        self.cue_list[index]['fade_out'] = max(0.0, min(60.0, fade_value))
                    except ValueError:
                        pass  # Keep original value if invalid
                elif self.edit_column == '#6':  # rotate_left (position 6)
                    try:
                        rot_value = float(new_value)
                        self.cue_list[index]['rotate_left'] = max(0.0, min(100.0, rot_value))
                    except ValueError:
                        pass
                elif self.edit_column == '#7':  # rotate_right (position 7)
                    try:
                        rot_value = float(new_value)
                        self.cue_list[index]['rotate_right'] = max(0.0, min(100.0, rot_value))
                    except ValueError:
                        pass
                
                # Refresh the display
                self.refresh_cue_list()
                self.select_cue_by_index(index)
                
            self.cleanup_inline_edit()
            
    def cancel_inline_edit(self, event=None):
        """Cancel inline editing without saving"""
        self.cleanup_inline_edit()
        
    def cleanup_inline_edit(self):
        """Clean up inline editing widgets"""
        if hasattr(self, 'edit_entry') and self.edit_entry:
            self.edit_entry.destroy()
            self.edit_entry = None
        if hasattr(self, 'edit_item'):
            del self.edit_item
        if hasattr(self, 'edit_column'):
            del self.edit_column

        # RE-ENABLE KEYBINDS after editing is complete
        self.enable_keybinds_after_editing()

    def disable_keybinds_for_editing(self):
        """Disable all keybinds during text editing to prevent accidental playback"""
        # Store the original keybind state
        self._keybinds_disabled_for_editing = True
        self._original_keybinds_enabled = self.keybinds_enabled

        # Temporarily disable keybinds
        self.keybinds_enabled = False
        self.setup_keybinds()  # This will clear all keybinds except F11

        # Update status to show editing mode
        self.status_var.set("✏️ EDITING MODE - All keyboard shortcuts disabled")

    def enable_keybinds_after_editing(self):
        """Re-enable keybinds after text editing is complete"""
        if hasattr(self, '_keybinds_disabled_for_editing') and self._keybinds_disabled_for_editing:
            # Restore original keybind state (always True now)
            self.keybinds_enabled = True
            self.setup_keybinds()  # Restore all keybinds

            # Clear editing flags
            self._keybinds_disabled_for_editing = False
            delattr(self, '_original_keybinds_enabled')

            # Update status
            self.status_var.set("⌨️ Keyboard shortcuts restored")

    # ------------------------------------------------------------
    # Dynamic projection window resize handling
    # ------------------------------------------------------------

    def on_projection_resize(self, event):
        """Ensure currently displayed media always fits resized window"""
        # Update stored size (event provides new size)
        self.projection_size = (event.width, event.height)

        # If not currently playing anything, nothing more to do
        if not self.is_playing or self.current_cue_index < 0 or self.current_cue_index >= len(self.cue_list):
            return

        cue = self.cue_list[self.current_cue_index]

        # Ignore blackout cue
        if cue['path'] == 'BLACKOUT':
            return

        file_ext = os.path.splitext(cue['path'])[1].lower()

        # For images: re-display with new dimensions (clear cache to force re-process)
        if file_ext in self.supported_formats['images']:
            self.clear_media_cache()
            # Debounce rapid resize events by scheduling after idle if one not already pending
            if hasattr(self, '_resize_after_id') and self._resize_after_id:
                self.root.after_cancel(self._resize_after_id)
            self._resize_after_id = self.root.after(100, lambda: self.display_image(cue['path']))
        # Videos pick up new size automatically in playback loop via self.projection_size

    # ------------------------------------------------------------------
    # Enhanced Image Rotation System
    # ------------------------------------------------------------------
    def stop_image_rotation(self):
        """Stop any ongoing image rotation animation using the enhanced engine."""
        if self.rotation_engine:
            self.rotation_engine.stop_rotation()

    def _init_image_rotation(self, image_path):
        """Initialize rotation for current cue - handles both starting and stopping rotation."""
        # Validate current cue and rotation engine
        if not (0 <= self.current_cue_index < len(self.cue_list)) or not self.rotation_engine:
            return

        cue = self.cue_list[self.current_cue_index]

        left_speed = float(cue.get('rotate_left', 0))
        right_speed = float(cue.get('rotate_right', 0))

        # If no rotation requested, stop any existing rotation
        if left_speed <= 0 and right_speed <= 0:
            if self.rotation_engine.is_active:
                print("🔄 Stopping rotation - cue has no rotation settings")
                self.rotation_engine.stop_rotation()
            return

        # Start rotation with new settings
        success = self.rotation_engine.start_rotation(left_speed, right_speed, image_path)

        if success:
            if left_speed > 0 and right_speed > 0:
                print(f"🔄 Ping-pong rotation started: Left {left_speed}, Right {right_speed}")
            elif left_speed > 0:
                print(f"🔄 Left rotation started: {left_speed}")
            else:
                print(f"🔄 Right rotation started: {right_speed}")
        else:
            print("❌ Failed to start rotation")

    def _init_video_rotation(self, video_path):
        """Initialize rotation for video playback - handles both starting and stopping rotation."""
        # Validate current cue and rotation engine
        if not (0 <= self.current_cue_index < len(self.cue_list)) or not self.rotation_engine:
            return

        cue = self.cue_list[self.current_cue_index]

        left_speed = float(cue.get('rotate_left', 0))
        right_speed = float(cue.get('rotate_right', 0))

        # If no rotation requested, stop any existing rotation
        if left_speed <= 0 and right_speed <= 0:
            if self.rotation_engine.is_active:
                print("🔄 Stopping video rotation - cue has no rotation settings")
                self.rotation_engine.stop_rotation()
            return

        # Start rotation with new settings (no image path for video mode)
        success = self.rotation_engine.start_rotation(left_speed, right_speed, None)

        if success:
            if left_speed > 0 and right_speed > 0:
                print(f"🔄 Video ping-pong rotation started: Left {left_speed}, Right {right_speed}")
            elif left_speed > 0:
                print(f"🔄 Video left rotation started: {left_speed}")
            else:
                print(f"🔄 Video right rotation started: {right_speed}")
        else:
            print("❌ Failed to start video rotation")







    def get_rotation_diagnostics(self) -> Dict[str, Any]:
        """Get comprehensive rotation system diagnostics"""
        if not self.rotation_engine:
            return {'error': 'Rotation engine not initialized'}

        diagnostics = {
            'engine_status': 'active' if self.rotation_engine.is_active else 'inactive',
            'current_angle': round(self.rotation_engine.get_current_angle(), 2),
            'left_speed': self.rotation_engine.left_speed,
            'right_speed': self.rotation_engine.right_speed,
            'direction': 'left' if self.rotation_engine.direction == 1 else 'right',
            'is_ping_pong': self.rotation_engine.is_ping_pong,
            'is_video_mode': self.rotation_engine.is_video_mode,
            'update_interval': self.rotation_engine.update_interval,
            'degrees_per_second_multiplier': self.rotation_engine.degrees_per_second_multiplier
        }

        # Add application-level diagnostics
        diagnostics['application'] = {
            'current_cue_index': self.current_cue_index,
            'total_cues': len(self.cue_list),
            'is_playing': self.is_playing,
            'projection_window_exists': self.projection_window and self.projection_window.winfo_exists()
        }

        # Add current cue rotation settings if available
        if 0 <= self.current_cue_index < len(self.cue_list):
            cue = self.cue_list[self.current_cue_index]
            diagnostics['current_cue'] = {
                'name': cue.get('name', 'Unknown'),
                'type': cue.get('type', 'Unknown'),
                'rotate_left': cue.get('rotate_left', 0),
                'rotate_right': cue.get('rotate_right', 0)
            }

        return diagnostics

    def print_rotation_diagnostics(self):
        """Print rotation diagnostics to console for debugging"""
        diagnostics = self.get_rotation_diagnostics()
        print("\n🔄 ROTATION SYSTEM DIAGNOSTICS")
        print("=" * 50)

        for category, data in diagnostics.items():
            print(f"\n{category.upper()}:")
            if isinstance(data, dict):
                for key, value in data.items():
                    print(f"  {key}: {value}")
            else:
                print(f"  {data}")

        print("=" * 50)

    def migrate_cue_rotation_data(self):
        """Ensure all cues have simple rotation fields"""
        migrated_count = 0

        for cue in self.cue_list:
            # Ensure rotate_left and rotate_right fields exist
            if 'rotate_left' not in cue:
                cue['rotate_left'] = 0.0
                migrated_count += 1
            if 'rotate_right' not in cue:
                cue['rotate_right'] = 0.0
                migrated_count += 1

        if migrated_count > 0:
            print(f"🔄 Migration complete: Added rotation fields to cues")
            self.refresh_cue_list()
        else:
            print("🔄 All cues have rotation fields")

    def test_rotation_system(self):
        """Test the simple rotation system"""
        if not self.rotation_engine:
            print("❌ Rotation engine not initialized")
            return

        print("🔄 Testing Simple Rotation System")
        print("=" * 40)

        # Create a test image if we have cues
        if self.cue_list:
            test_cue_index = 0
            test_cue = self.cue_list[test_cue_index]

            if test_cue['type'] == 'IMAGE':
                print(f"Testing with image: {test_cue['name']}")

                # Test different rotation modes
                test_cases = [
                    (50, 0, "Left rotation only"),
                    (0, 50, "Right rotation only"),
                    (30, 70, "Ping-pong (both speeds)")
                ]

                for left_speed, right_speed, description in test_cases:
                    print(f"\n🔄 Testing {description}...")

                    # Start rotation
                    success = self.rotation_engine.start_rotation(left_speed, right_speed, test_cue['path'])

                    if success:
                        print(f"  ✅ {description} started successfully")
                        # Let it run for a moment
                        self.root.after(2000, self.rotation_engine.stop_rotation)
                    else:
                        print(f"  ❌ {description} failed to start")

                    # Wait between tests
                    self.root.after(2500)

                print("\n🔄 Rotation system test complete")
            else:
                print("❌ First cue is not an image - cannot test rotation")
        else:
            print("❌ No cues available for testing")

    def validate_rotation_system(self):
        """Validate the simple rotation system integrity"""
        issues = []

        # Check rotation engine
        if not self.rotation_engine:
            issues.append("Rotation engine not initialized")

        # Check projection window
        if not self.projection_window or not self.projection_window.winfo_exists():
            issues.append("Projection window not available")

        # Check cue data integrity
        for i, cue in enumerate(self.cue_list):
            if cue['type'] in ['IMAGE', 'VIDEO']:
                # Check rotation fields
                if 'rotate_left' not in cue:
                    issues.append(f"Cue {i+1} missing rotate_left field")

                if 'rotate_right' not in cue:
                    issues.append(f"Cue {i+1} missing rotate_right field")

                # Validate rotation values (should be 0-100)
                try:
                    left_val = float(cue.get('rotate_left', 0))
                    if left_val < 0 or left_val > 100:
                        issues.append(f"Cue {i+1} rotate_left value out of range (0-100): {left_val}")
                except (ValueError, TypeError):
                    issues.append(f"Cue {i+1} rotate_left is not a valid number")

                try:
                    right_val = float(cue.get('rotate_right', 0))
                    if right_val < 0 or right_val > 100:
                        issues.append(f"Cue {i+1} rotate_right value out of range (0-100): {right_val}")
                except (ValueError, TypeError):
                    issues.append(f"Cue {i+1} rotate_right is not a valid number")

        # Report results
        if issues:
            print("🔄 ROTATION SYSTEM VALIDATION ISSUES:")
            for issue in issues:
                print(f"  ❌ {issue}")
            return False
        else:
            print("🔄 Simple rotation system validation passed ✅")
            return True

    def test_rotation_dialogs(self):
        """Test the rotation mode and easing dialogs"""
        if not self.cue_list:
            messagebox.showinfo("Test Dialogs", "Please add some image cues first to test the dialogs.")
            return

        # Find first image cue
        image_cue_index = -1
        for i, cue in enumerate(self.cue_list):
            if cue['type'] == 'IMAGE':
                image_cue_index = i
                break

        if image_cue_index == -1:
            messagebox.showinfo("Test Dialogs", "Please add some image cues first to test the dialogs.")
            return

        # Old rotation mode dialog removed - now using simple rotate_left/rotate_right values
        print(f"🔧 Old rotation mode dialog removed - use rotate_left/rotate_right columns instead")

    # Removed old show_rotation_help - now using simple rotate_left/rotate_right system

    def show_keyboard_help(self):
        """Show keyboard shortcuts help"""
        help_text = """
⌨️ KEYBOARD SHORTCUTS

PLAYBACK CONTROLS:
• Space - Play/Pause current cue
• Up Arrow - Previous cue
• Down Arrow - Next cue
• Escape - Stop playback

DISPLAY CONTROLS:
• F11 - Toggle fullscreen
• Shift+F11 - Cycle displays (when fullscreen)
• F12 - Toggle keyboard shortcuts on/off

EDITING:
• Ctrl+C - Copy selected cue(s)
• Ctrl+V - Paste cue(s)
• Ctrl+Z - Undo last action
• Ctrl+Shift+Z - Redo action
• Delete - Remove selected cue(s)

ROTATION SYSTEM:
• Ctrl+R - Show rotation diagnostics
• Double-click rotate_left column - Edit left rotation speed (0-100)
• Double-click rotate_right column - Edit right rotation speed (0-100)

GENERAL:
• Double-click fade columns - Edit fade times
• Double-click notes column - Edit notes
• Double-click fill column - Toggle fill screen
        """

        dialog = tk.Toplevel(self.root)
        dialog.title("Keyboard Shortcuts")
        dialog.geometry("500x450")
        dialog.configure(bg='#2b2b2b')
        dialog.transient(self.root)

        text_widget = tk.Text(dialog, wrap=tk.WORD, bg='#2b2b2b', fg='white',
                             font=('Consolas', 10), padx=20, pady=20)
        text_widget.pack(fill=tk.BOTH, expand=True)
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)

        # Close button
        close_btn = tk.Button(dialog, text="Close", command=dialog.destroy,
                             bg='#4CAF50', fg='white', font=('Arial', 10, 'bold'))
        close_btn.pack(pady=10)

    def show_rotation_settings(self):
        """Show advanced rotation settings dialog"""
        if not self.rotation_engine:
            messagebox.showwarning("Warning", "Rotation engine not initialized. Please open projection window first.")
            return

        settings_window = tk.Toplevel(self.root)
        settings_window.title("🔄 Advanced Rotation Settings")
        settings_window.geometry("500x600")
        settings_window.configure(bg='#2b2b2b')
        settings_window.transient(self.root)
        settings_window.grab_set()

        # Center the window
        settings_window.update_idletasks()
        x = (settings_window.winfo_screenwidth() // 2) - (500 // 2)
        y = (settings_window.winfo_screenheight() // 2) - (600 // 2)
        settings_window.geometry(f"500x600+{x}+{y}")

        main_frame = ttk.Frame(settings_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        title_label = ttk.Label(main_frame, text="🔄 Ultimate Rotation Settings",
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 20))

        # Backend Selection
        backend_frame = ttk.LabelFrame(main_frame, text="Rotation Backend", padding=10)
        backend_frame.pack(fill=tk.X, pady=(0, 15))

        backend_var = tk.StringVar(value=self.rotation_engine.backend.value)

        ttk.Label(backend_frame, text="Choose rotation processing backend:").pack(anchor=tk.W)

        backends = [
            ("OpenCV (Fastest)", RotationBackend.OPENCV.value),
            ("PIL (Compatible)", RotationBackend.PIL.value),
            ("Hybrid (Best)", RotationBackend.HYBRID.value)
        ]

        for text, value in backends:
            ttk.Radiobutton(backend_frame, text=text, variable=backend_var,
                           value=value).pack(anchor=tk.W, pady=2)

        # Quality Override
        quality_frame = ttk.LabelFrame(main_frame, text="Quality Settings", padding=10)
        quality_frame.pack(fill=tk.X, pady=(0, 15))

        adaptive_var = tk.BooleanVar(value=self.rotation_engine.adaptive_quality)
        ttk.Checkbutton(quality_frame, text="Adaptive Quality (Recommended)",
                       variable=adaptive_var).pack(anchor=tk.W)

        ttk.Label(quality_frame, text="Manual Quality Override:").pack(anchor=tk.W, pady=(10, 5))

        quality_var = tk.StringVar(value="auto")
        if self.rotation_engine.quality_override:
            quality_var.set(self.rotation_engine.quality_override.value)

        qualities = [
            ("Auto (Adaptive)", "auto"),
            ("Ultra (Slowest, Best)", RotationQuality.ULTRA.value),
            ("High (Recommended)", RotationQuality.HIGH.value),
            ("Medium (Balanced)", RotationQuality.MEDIUM.value),
            ("Fast (Performance)", RotationQuality.FAST.value),
            ("Fastest (Speed)", RotationQuality.FASTEST.value)
        ]

        for text, value in qualities:
            ttk.Radiobutton(quality_frame, text=text, variable=quality_var,
                           value=value).pack(anchor=tk.W, pady=1)

        # Performance Settings
        perf_frame = ttk.LabelFrame(main_frame, text="Performance Settings", padding=10)
        perf_frame.pack(fill=tk.X, pady=(0, 15))

        temporal_var = tk.BooleanVar(value=self.rotation_engine.temporal_smoothing)
        ttk.Checkbutton(perf_frame, text="Temporal Smoothing (Ultra-smooth rotation)",
                       variable=temporal_var).pack(anchor=tk.W)

        ttk.Label(perf_frame, text="Maximum FPS:").pack(anchor=tk.W, pady=(10, 5))
        max_fps_var = tk.IntVar(value=self.rotation_engine.max_fps)
        max_fps_scale = ttk.Scale(perf_frame, from_=30, to=240, variable=max_fps_var,
                                 orient=tk.HORIZONTAL)
        max_fps_scale.pack(fill=tk.X, pady=(0, 5))

        max_fps_label = ttk.Label(perf_frame, text=f"Current: {max_fps_var.get()} FPS")
        max_fps_label.pack(anchor=tk.W)

        def update_fps_label(*args):
            max_fps_label.config(text=f"Current: {max_fps_var.get()} FPS")
        max_fps_var.trace('w', update_fps_label)

        # Current Status
        status_frame = ttk.LabelFrame(main_frame, text="Current Status", padding=10)
        status_frame.pack(fill=tk.X, pady=(0, 15))

        stats = self.rotation_engine.get_performance_stats()

        # Get current quality mode from parent app
        current_quality_mode = "4K"
        if hasattr(self, '_parent_app') and self._parent_app and hasattr(self._parent_app, 'quality_mode'):
            current_quality_mode = self._parent_app.quality_mode

        status_text = f"""Active: {stats['active']}
Media Quality Mode: {current_quality_mode} (affects rotation FPS)
Current Rotation Quality: {stats['quality'].upper()}
Backend: {stats['backend'].upper()}
Current FPS: {stats['fps']}
FPS Range: {self.rotation_engine.min_fps}-{self.rotation_engine.max_fps} FPS
Cache Size: {stats['cache_size']} matrices
Temporal Smoothing: {stats['temporal_smoothing']}"""

        ttk.Label(status_frame, text=status_text, font=('Courier', 9)).pack(anchor=tk.W)

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(15, 0))

        def apply_settings():
            # Apply backend
            try:
                new_backend = RotationBackend(backend_var.get())
                self.rotation_engine.set_backend(new_backend)
            except ValueError:
                pass

            # Apply quality settings
            self.rotation_engine.adaptive_quality = adaptive_var.get()

            if quality_var.get() == "auto":
                self.rotation_engine.set_quality_override(None)
            else:
                try:
                    quality_override = RotationQuality(quality_var.get())
                    self.rotation_engine.set_quality_override(quality_override)
                except ValueError:
                    pass

            # Apply performance settings
            self.rotation_engine.set_temporal_smoothing(temporal_var.get())
            self.rotation_engine.max_fps = max_fps_var.get()

            messagebox.showinfo("Success", "Rotation settings applied successfully!")
            settings_window.destroy()

        def reset_defaults():
            backend_var.set(RotationBackend.HYBRID.value)
            adaptive_var.set(True)
            quality_var.set("auto")
            temporal_var.set(True)
            max_fps_var.set(120)

        ttk.Button(button_frame, text="Apply", command=apply_settings).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="Reset Defaults", command=reset_defaults).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="Cancel", command=settings_window.destroy).pack(side=tk.RIGHT, padx=(0, 5))

    def show_about(self):
        """Show about dialog"""
        about_text = """
🎭 THEATER PROJECTION SYSTEM
Ultimate Rotation Edition

A professional theater projection control system with the most
advanced image rotation technology available.

FEATURES:
• Multi-format media support (images & videos)
• Ultimate rotation system with adaptive quality
• Smooth fade transitions
• Multi-display fullscreen support
• Advanced keyboard shortcuts
• Real-time performance monitoring
• Professional cue management

ULTIMATE ROTATION SYSTEM:
• Up to 120+ FPS ultra-smooth animations
• LANCZOS interpolation for maximum quality
• OpenCV backend for ultimate performance
• Adaptive quality based on rotation speed
• Temporal smoothing for perfect motion
• Advanced memory management and caching
• Multiple rotation backends (OpenCV/PIL/Hybrid)
• Real-time performance optimization
• Multiple rotation modes
• Professional easing functions
• Error recovery and diagnostics

Version: Enhanced Rotation System
Built with: Python, Tkinter, PIL, OpenCV
        """

        dialog = tk.Toplevel(self.root)
        dialog.title("About Theater Projection System")
        dialog.geometry("450x400")
        dialog.configure(bg='#2b2b2b')
        dialog.transient(self.root)

        text_widget = tk.Text(dialog, wrap=tk.WORD, bg='#2b2b2b', fg='white',
                             font=('Arial', 10), padx=20, pady=20, justify=tk.CENTER)
        text_widget.pack(fill=tk.BOTH, expand=True)
        text_widget.insert(tk.END, about_text)
        text_widget.config(state=tk.DISABLED)

        # Close button
        close_btn = tk.Button(dialog, text="Close", command=dialog.destroy,
                             bg='#4CAF50', fg='white', font=('Arial', 10, 'bold'))
        close_btn.pack(pady=10)

if __name__ == "__main__":
    app = TheaterProjection()
    app.run()
